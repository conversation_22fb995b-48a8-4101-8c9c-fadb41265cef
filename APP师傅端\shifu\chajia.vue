<template>
	<view class="page">
		<view class="header">
			<view class="back-btn" @click="goBack">
				<text class="back-icon">←</text>
			</view>
			<view class="title">差价申请</view>
			<view class="placeholder"></view>
		</view>

		<view class="content">
			<!-- 订单信息 -->
			<view class="order-info" v-if="orderInfo">
				<view class="order-header">
					<view class="order-no">订单号：{{ orderInfo.orderCode }}</view>
					<view class="order-status">{{ getOrderStatusText(orderInfo.payType) }}</view>
				</view>
				<view class="order-detail">
					<view class="goods-info">
						<image :src="orderInfo.goodsCover" class="goods-image"></image>
						<view class="goods-text">
							<text class="goods-name">{{ orderInfo.goodsName }}</text>
							<text class="goods-price">￥{{ orderInfo.coachServicePrice }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 差价申请表单 -->
			<view class="form-container">
				<view class="form-title">申请差价</view>
				<u--form labelPosition="left" :model="diffApplyForm" :rules="diffApplyRules" ref="diffApplyForm">
					<u-form-item label="差价金额" prop="diffAmount" borderBottom ref="item1">
						<u--input v-model="diffApplyForm.diffAmount" placeholder="请输入差价金额" type="number"
							border="none"></u--input>
					</u-form-item>
					<u-form-item label="差价原因" borderBottom>
						<view class="reason-type-display">
							<text class="reason-type-text">配件不符合</text>
							<view class="reason-type-badge">类型: 1</view>
						</view>
					</u-form-item>
					<u-form-item label="原因详情" prop="reasonDetail" borderBottom ref="item3">
						<u--textarea v-model="diffApplyForm.reasonDetail" placeholder="请输入差价原因详情"
							count></u--textarea>
					</u-form-item>
					<u-form-item label="配件质保日期" prop="partsWarrantyPeriod" borderBottom ref="item4">
						<u--input v-model="diffApplyForm.partsWarrantyPeriod" placeholder="请选择配件质保日期"
							type="text" border="none" @click="showDatePicker" readonly></u--input>
					</u-form-item>
					<u-form-item label="配件图" borderBottom ref="item5">
						<view class="upload-container">
							<upload @upload="imgUploadDiff" @del="imgUploadDiff"
								:imagelist="diffApplyForm.partsimgs" imgtype="partsimgs" imgclass="parts-img"
								text="上传配件图" :imgsize="9"></upload>
						</view>
					</u-form-item>
				</u--form>
			</view>

			<!-- 已有差价申请记录 -->
			<view v-if="orderInfo && orderInfo.orderDiffPriceList && orderInfo.orderDiffPriceList.length > 0" class="sub_orders">
				<view class="sub_title">差价申请记录</view>
				<scroll-view class="sub_scroll_container" scroll-y="true" :style="{height: getScrollViewHeight(orderInfo.orderDiffPriceList.length) + 'rpx'}">
					<view class="sub_item" v-for="(diffItem, diffIndex) in orderInfo.orderDiffPriceList" :key="diffItem.id"
						@click.stop="">
						<view class="sub_head">
							<view class="sub_no">差价单号：{{ diffItem.diffCode }}</view>
							<view class="sub_status">{{ getDiffStatusText(diffItem.status) }}</view>
						</view>
						<view class="sub_content">
							<view class="sub_info_grid">
								<view class="sub_info_row">
									<view class="sub_info_item">
										<text class="sub_label">差价金额：</text>
										<text class="sub_value sub_amount_value">￥{{ diffItem.diffAmount }}</text>
									</view>
									<view class="sub_info_item">
										<text class="sub_label">质保日期：</text>
										<text class="sub_value sub_warranty_value">{{ formatWarrantyDate(diffItem.partsWarrantyPeriod) }}</text>
									</view>
								</view>
								<view class="sub_info_row">
									<view class="sub_info_item sub_reason_item">
										<text class="sub_label">原因：</text>
										<text class="sub_value sub_reason_value">{{ diffItem.reasonDetail }}</text>
									</view>
								</view>
								<view class="sub_info_row">
									<view class="sub_info_item">
										<text class="sub_label">申请时间：</text>
										<text class="sub_value sub_time_value">{{ diffItem.createdTime }}</text>
									</view>
								</view>
							</view>
							<view class="sub_actions">
								<view class="sub_qzf" v-if="diffItem.status === 0"
									@click.stop="showDiffCancelModal(diffItem)">
									取消差价
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 底部按钮 -->
		<view class="footer">
			<view class="btn-cancel" @click="goBack">取消</view>
			<view class="btn-confirm" @click="diffApplyConfirm">提交申请</view>
		</view>
	</view>
</template>

<script>
import Upload from '@/components/upload.vue'; // Import upload component

export default {
	components: {
		Upload,
	},
	data() {
		return {
			orderInfo: null, // 订单信息
			orderId: '', // 订单ID
			diffApplyForm: {
				diffAmount: '',
				reasonType: 1, // 差价原因类型，目前固定为1代表配件不符合
				reasonDetail: '',
				partsWarrantyPeriod: '', // 配件质保日期
				partsimgs: [], // 配件图片
			},
			diffApplyRules: {
				diffAmount: [{
					required: true,
					message: '请输入差价金额',
					trigger: ['blur', 'change']
				}, {
					validator: (rule, value, callback) => {
						return value >= 0.01;
					},
					message: '差价金额必须大于等于0.01',
					trigger: ['blur', 'change']
				}],
				reasonDetail: [{
					required: true,
					message: '请输入差价原因详情',
					trigger: ['blur', 'change']
				}],
				partsWarrantyPeriod: [{
					required: true,
					message: '请选择配件质保日期',
					trigger: ['blur', 'change']
				}],
			},
			pay_typeArr: ['', '待支付', '已支付', '已接单', '上门中', '待服务', '服务中', '已完成', '售后'],
		}
	},
	onLoad(options) {
		if (options.orderId) {
			this.orderId = options.orderId;
			this.loadOrderInfo();
		} else {
			uni.showToast({
				icon: 'none',
				title: '订单信息缺失'
			});
			setTimeout(() => {
				this.goBack();
			}, 1500);
		}
	},
	onReady() {
		// 在页面准备好后设置表单规则
		this.$nextTick(() => {
			if (this.$refs.diffApplyForm && this.$refs.diffApplyForm.setRules) {
				this.$refs.diffApplyForm.setRules(this.diffApplyRules);
			}
		});
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 加载订单信息
		async loadOrderInfo() {
			try {
				// 这里需要根据实际API调用获取订单详情
				// 暂时使用从storage获取的数据
				const orderDetails = uni.getStorageSync('orderdetails');
				if (orderDetails && orderDetails.id == this.orderId) {
					this.orderInfo = orderDetails;
				} else {
					// 如果storage中没有数据，可以调用API获取
					uni.showToast({
						icon: 'none',
						title: '订单信息获取失败'
					});
				}
			} catch (error) {
				console.error('加载订单信息失败:', error);
				uni.showToast({
					icon: 'none',
					title: '订单信息获取失败'
				});
			}
		},

		// 获取订单状态文本
		getOrderStatusText(payType) {
			return this.pay_typeArr[payType] || '未知状态';
		},

		// 获取差价申请状态文本
		getDiffStatusText(status) {
			const statusMap = {
				'-1': '已取消',
				0: '待确认',
				1: '已确认待支付',
				2: '已支付',
				3: '已拒绝'
			};
			return statusMap[status] || '未知状态';
		},

		// 格式化配件质保日期
		formatWarrantyDate(timestamp) {
			if (!timestamp) {
				return '无质保信息';
			}
			try {
				const date = new Date(timestamp);
				if (isNaN(date.getTime())) {
					return '无效日期';
				}
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			} catch (error) {
				console.error('格式化质保日期失败:', error);
				return '格式错误';
			}
		},

		// 获取滚动视图高度
		getScrollViewHeight(itemCount) {
			// 每个项目大约150rpx高度，最多显示3个项目，超过则可滚动
			const maxHeight = 450; // 3 * 150rpx
			const itemHeight = 150;
			const calculatedHeight = Math.min(itemCount * itemHeight, maxHeight);
			return calculatedHeight;
		},

		// 显示日期选择器
		showDatePicker() {
			const currentDate = new Date();
			const minDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000); // 明天
			const maxDate = new Date(currentDate.getTime() + 5 * 365 * 24 * 60 * 60 * 1000); // 5年后

			uni.showDatePicker({
				current: this.diffApplyForm.partsWarrantyPeriod || this.formatDateForPicker(minDate),
				start: this.formatDateForPicker(minDate),
				end: this.formatDateForPicker(maxDate),
				success: (res) => {
					this.diffApplyForm.partsWarrantyPeriod = res.date;
				}
			});
		},

		// 格式化日期为选择器需要的格式
		formatDateForPicker(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		},

		// 处理差价申请中的图片上传
		imgUploadDiff(e) {
			console.log('imgUploadDiff event:', e);
			const { imagelist, imgtype } = e;
			this.$set(this.diffApplyForm, imgtype, imagelist);
		},

		// 显示取消差价申请确认弹窗
		showDiffCancelModal(diffItem) {
			uni.showModal({
				title: '取消差价申请',
				content: `确定要取消差价单号 ${diffItem.diffCode} 的申请吗？`,
				confirmText: '确定',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						this.diffCancel(diffItem);
					}
				}
			});
		},

		// 取消差价申请
		async diffCancel(diffItem) {
			try {
				const res = await this.$api.shifu.diffCancel({
					id: diffItem.id
				});
				if (res.code === "200") {
					uni.showToast({
						title: '差价申请已取消',
						icon: 'success'
					});
					// 重新加载订单信息
					this.loadOrderInfo();
				} else {
					uni.showToast({
						title: res.msg || '取消失败',
						icon: 'none'
					});
				}
			} catch (err) {
				uni.showToast({
					title: '请求失败',
					icon: 'none'
				});
				console.error('Error in diffCancel:', err);
			}
		},

		// 提交差价申请
		async diffApplyConfirm() {
			// Validate the form before submitting
			try {
				// 检查表单引用是否存在
				if (!this.$refs.diffApplyForm || !this.$refs.diffApplyForm.validate) {
					uni.showToast({
						icon: 'none',
						title: '表单未准备就绪，请稍后重试'
					});
					return;
				}
				await this.$refs.diffApplyForm.validate();

				// Form is valid, proceed with API call
				if (!this.orderInfo) {
					uni.showToast({
						icon: 'none',
						title: '订单信息缺失'
					});
					return;
				}

				try {
					// 准备配件图片数据 - 根据API文档，partsImgs是string类型，需要转换
					const partsImgsString = this.diffApplyForm.partsimgs.map(img => img.path).join(',');

					// 将质保日期转换为时间戳
					let partsWarrantyPeriodTimestamp = null;
					if (this.diffApplyForm.partsWarrantyPeriod) {
						partsWarrantyPeriodTimestamp = new Date(this.diffApplyForm.partsWarrantyPeriod).getTime();
					}

					const res = await this.$api.shifu.diffApply({
						orderId: this.orderInfo.id,
						diffAmount: parseFloat(this.diffApplyForm.diffAmount),
						reasonType: this.diffApplyForm.reasonType, // 差价原因类型(1配件不符合)
						reasonDetail: this.diffApplyForm.reasonDetail,
						partsImgs: partsImgsString, // 配件图片，string类型，多个图片用逗号分隔
						partsWarrantyPeriod: partsWarrantyPeriodTimestamp // 配件质保日期时间戳
					});

					if (res.code === "200") {
						uni.showToast({
							title: '差价申请成功',
							icon: 'success'
						});
						// 返回上一页
						setTimeout(() => {
							this.goBack();
						}, 1500);
					} else {
						uni.showToast({
							title: res.msg || '差价申请失败',
							icon: 'none'
						});
					}
				} catch (err) {
					uni.showToast({
						title: '请求失败',
						icon: 'none'
					});
					console.error('Error in diffApply:', err);
				}
			} catch (errors) {
				console.error('Form validation failed:', errors);
				uni.showToast({
					icon: 'none',
					title: '请检查填写信息'
				});
			}
		},
	}
}
</script>

<style scoped lang="scss">
.page {
	background-color: #F8F8F8;
	min-height: 100vh;
	padding-bottom: 120rpx; // 为底部按钮留出空间
}

.header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
	height: 88rpx;
	background: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	border-bottom: 1rpx solid #f0f0f0;

	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.back-icon {
			font-size: 36rpx;
			color: #333;
		}
	}

	.title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
	}

	.placeholder {
		width: 60rpx;
	}
}

.content {
	padding-top: 88rpx;
	padding: 88rpx 30rpx 20rpx;
}

.order-info {
	background: #FFFFFF;
	border-radius: 24rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;

	.order-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;

		.order-no {
			font-size: 24rpx;
			color: #666;
		}

		.order-status {
			font-size: 24rpx;
			color: #2E80FE;
			font-weight: 500;
		}
	}

	.order-detail {
		.goods-info {
			display: flex;
			align-items: center;

			.goods-image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 12rpx;
				margin-right: 20rpx;
			}

			.goods-text {
				flex: 1;
				display: flex;
				flex-direction: column;

				.goods-name {
					font-size: 28rpx;
					color: #333;
					margin-bottom: 10rpx;
				}

				.goods-price {
					font-size: 32rpx;
					color: #ff6b35;
					font-weight: 600;
				}
			}
		}
	}
}

.form-container {
	background: #FFFFFF;
	border-radius: 24rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;

	.form-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 30rpx;
	}
}

.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #FFFFFF;
	padding: 20rpx 30rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	gap: 20rpx;

	.btn-cancel,
	.btn-confirm {
		flex: 1;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 8rpx;
		font-size: 28rpx;
		transition: all 0.2s;
	}

	.btn-cancel {
		background: #f8f8f8;
		color: #666;
		border: 1rpx solid #ddd;
	}

	.btn-cancel:active {
		background: #e8e8e8;
	}

	.btn-confirm {
		background: #2E80FE;
		color: #fff;
		border: 1rpx solid #2E80FE;
	}

	.btn-confirm:active {
		background: #1a6fd1;
	}
}

// 差价申请记录样式
.sub_orders {
	background: #FFFFFF;
	border-radius: 24rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;

	.sub_title {
		font-size: 26rpx;
		font-weight: 500;
		color: #666;
		margin-bottom: 20rpx;
	}

	.sub_scroll_container {
		max-height: 450rpx;
		overflow-y: auto;
	}

	.sub_item {
		background: #f8f9fa;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 15rpx;

		.sub_head {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 15rpx;

			.sub_no {
				font-size: 22rpx;
				color: #666;
				max-width: 400rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			.sub_status {
				font-size: 22rpx;
				color: #2E80FE;
				font-weight: 500;
			}
		}

		.sub_content {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;

			.sub_info_grid {
				flex: 1;
				margin-right: 20rpx;

				.sub_info_row {
					display: flex;
					margin-bottom: 12rpx;
					gap: 20rpx;

					&:last-child {
						margin-bottom: 0;
					}

					.sub_info_item {
						display: flex;
						align-items: center;
						flex: 1;
						min-width: 0;

						&.sub_reason_item {
							flex: 2;
						}

						.sub_label {
							font-size: 20rpx;
							color: #999;
							margin-right: 8rpx;
							flex-shrink: 0;
						}

						.sub_value {
							font-size: 22rpx;
							color: #333;
							flex: 1;
							min-width: 0;

							&.sub_amount_value {
								font-weight: 500;
								color: #ff6b35;
							}

							&.sub_warranty_value {
								color: #2E80FE;
								font-weight: 500;
							}

							&.sub_reason_value {
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
							}

							&.sub_time_value {
								font-size: 20rpx;
								color: #999;
							}
						}
					}
				}
			}

			.sub_actions {
				flex-shrink: 0;
				align-self: flex-start;

				.sub_qzf {
					width: 120rpx;
					height: 40rpx;
					background: #ff6b6b;
					border-radius: 40rpx;
					font-size: 18rpx;
					font-weight: 400;
					line-height: 40rpx;
					text-align: center;
					color: #fff;
				}
			}
		}
	}
}

// 表单样式
.upload-container {
	margin-top: 16rpx;
	padding: 20rpx;
	border: 1rpx dashed #ccc;
	border-radius: 8rpx;
	background: #fafafa;
}

.parts-img {
	width: 120rpx;
	height: 120rpx;
	margin-right: 12rpx;
	margin-bottom: 12rpx;
	border-radius: 8rpx;
}

.reason-type-display {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx;
	background: #f8f8f8;
	border: 1rpx solid #e0e0e0;
	border-radius: 8rpx;
}

.reason-type-text {
	font-size: 28rpx;
	color: #333;
}

.reason-type-badge {
	background: #2E80FE;
	color: #fff;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

// 表单项样式优化
/deep/ .u-form-item {
	margin-bottom: 24rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

/deep/ .u-form-item:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

/deep/ .u-form-item__label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 12rpx;
	font-weight: 500;
}

/deep/ .u--input__content {
	background: #f8f8f8;
	border: 1rpx solid #e0e0e0;
	border-radius: 8rpx;
	padding: 16rpx;
	font-size: 28rpx;
	color: #333;
}

/deep/ .u--textarea__content {
	background: #f8f8f8;
	border: 1rpx solid #e0e0e0;
	border-radius: 8rpx;
	padding: 16rpx;
	font-size: 28rpx;
	color: #333;
	min-height: 120rpx;
}
</style>
