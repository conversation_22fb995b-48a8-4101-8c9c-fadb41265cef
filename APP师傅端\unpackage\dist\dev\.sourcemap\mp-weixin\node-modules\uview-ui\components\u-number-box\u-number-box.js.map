{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-number-box/u-number-box.vue?8505", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-number-box/u-number-box.vue?de09", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-number-box/u-number-box.vue?a055", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-number-box/u-number-box.vue?a1a2", "uni-app:///node_modules/uview-ui/components/u-number-box/u-number-box.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-number-box/u-number-box.vue?f0e7", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-number-box/u-number-box.vue?9e1d"], "names": ["name", "mixins", "data", "currentValue", "longPressTimer", "watch", "watchChange", "value", "computed", "getCursorSpacing", "buttonStyle", "backgroundColor", "height", "color", "style", "inputStyle", "width", "isDisabled", "mounted", "methods", "init", "format", "filter", "check", "onFocus", "event", "onBlur", "onInput", "e", "formatted", "emitChange", "onChange", "type", "add", "clickHandler", "longPressStep", "onTouchStart", "onTouchEnd", "clearTimeout"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAA21B,CAAgB,22BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC6E/2B;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA,eAgCA;EACAA;EACAC;EACAC;IACA;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACAC;UACAC;UACAC;QACA;QACA;UACAC;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAF;QACAF;QACAC;QACAI;MACA;MACA;IACA;IACA;IACAV;MACA;IACA;IACAW;MAAA;MACA;QACA;UACA;UACA,OACA,mBACA,sBACA;QAEA;QACA;QACA,OACA,mBACA,uBACA;MAEA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACA;IACAC;MACAd;MACA;MACAA;MACA;MACAA;MACA;MACA;QACAA;MACA;MACA;IACA;IACA;IACAe;MACA;MACAf;MACA;MACA;QACAA;MACA;MACA;IACA;IACAgB;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA,oDACAC;QACAzB;MAAA,GACA;IACA;IACA;IACA0B;MACA;MACA;MACA;MACA,WACA,wCACAD;QACAzB;MAAA,GAEA;IACA;IACA;IACA2B;MACA,WAEAC;QAAA,kBADArB;QAAAA;MAEA;MACA;MACA;MACA;MACA;QACA;QACAsB;MACA;MACAA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;UACA;UACA;UACA;QACA;MACA;MACA;QACAvB;QACAP;MACA;IACA;IACA+B;MACA,IACAC,OACA,KADAA;MAEA;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;QAAA;MAAA;MAAA;QAAA;MAAA;MAAA;IAAA;MACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrVA;AAAA;AAAA;AAAA;AAAkmD,CAAgB,sjDAAG,EAAC,C;;;;;;;;;;;ACAtnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-number-box/u-number-box.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-number-box.vue?vue&type=template&id=18418972&scoped=true&\"\nvar renderjs\nimport script from \"./u-number-box.vue?vue&type=script&lang=js&\"\nexport * from \"./u-number-box.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-number-box.vue?vue&type=style&index=0&id=18418972&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"18418972\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-number-box/u-number-box.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-number-box.vue?vue&type=template&id=18418972&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 =\n    !(_vm.showMinus && _vm.$slots.minus) && _vm.showMinus\n      ? _vm.__get_style([_vm.buttonStyle(\"minus\")])\n      : null\n  var m0 =\n    !(_vm.showMinus && _vm.$slots.minus) && _vm.showMinus\n      ? _vm.isDisabled(\"minus\")\n      : null\n  var m1 =\n    !(_vm.showMinus && _vm.$slots.minus) && _vm.showMinus\n      ? _vm.isDisabled(\"minus\")\n      : null\n  var s1 = _vm.__get_style([_vm.inputStyle])\n  var s2 =\n    !(_vm.showPlus && _vm.$slots.plus) && _vm.showPlus\n      ? _vm.__get_style([_vm.buttonStyle(\"plus\")])\n      : null\n  var m2 =\n    !(_vm.showPlus && _vm.$slots.plus) && _vm.showPlus\n      ? _vm.isDisabled(\"plus\")\n      : null\n  var m3 =\n    !(_vm.showPlus && _vm.$slots.plus) && _vm.showPlus\n      ? _vm.isDisabled(\"plus\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        m0: m0,\n        m1: m1,\n        s1: s1,\n        s2: s2,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-number-box.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-number-box.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-number-box\">\n\t\t<view\n\t\t    class=\"u-number-box__slot\"\n\t\t    @tap.stop=\"clickHandler('minus')\"\n\t\t    @touchstart=\"onTouchStart('minus')\"\n\t\t    @touchend.stop=\"clearTimeout\"\n\t\t    v-if=\"showMinus && $slots.minus\"\n\t\t>\n\t\t\t<slot name=\"minus\" />\n\t\t</view>\n\t\t<view\n\t\t    v-else-if=\"showMinus\"\n\t\t    class=\"u-number-box__minus\"\n\t\t    @tap.stop=\"clickHandler('minus')\"\n\t\t    @touchstart=\"onTouchStart('minus')\"\n\t\t    @touchend.stop=\"clearTimeout\"\n\t\t    hover-class=\"u-number-box__minus--hover\"\n\t\t    hover-stay-time=\"150\"\n\t\t    :class=\"{ 'u-number-box__minus--disabled': isDisabled('minus') }\"\n\t\t    :style=\"[buttonStyle('minus')]\"\n\t\t>\n\t\t\t<u-icon\n\t\t\t    name=\"minus\"\n\t\t\t    :color=\"isDisabled('minus') ? '#c8c9cc' : '#323233'\"\n\t\t\t    size=\"15\"\n\t\t\t    bold\n\t\t\t\t:customStyle=\"iconStyle\"\n\t\t\t></u-icon>\n\t\t</view>\n\n\t\t<slot name=\"input\">\n\t\t\t<input\n\t\t\t    :disabled=\"disabledInput || disabled\"\n\t\t\t    :cursor-spacing=\"getCursorSpacing\"\n\t\t\t    :class=\"{ 'u-number-box__input--disabled': disabled || disabledInput }\"\n\t\t\t    v-model=\"currentValue\"\n\t\t\t    class=\"u-number-box__input\"\n\t\t\t    @blur=\"onBlur\"\n\t\t\t    @focus=\"onFocus\"\n\t\t\t    @input=\"onInput\"\n\t\t\t    type=\"number\"\n\t\t\t    :style=\"[inputStyle]\"\n\t\t\t/>\n\t\t</slot>\n\t\t<view\n\t\t    class=\"u-number-box__slot\"\n\t\t    @tap.stop=\"clickHandler('plus')\"\n\t\t    @touchstart=\"onTouchStart('plus')\"\n\t\t    @touchend.stop=\"clearTimeout\"\n\t\t    v-if=\"showPlus && $slots.plus\"\n\t\t>\n\t\t\t<slot name=\"plus\" />\n\t\t</view>\n\t\t<view\n\t\t    v-else-if=\"showPlus\"\n\t\t    class=\"u-number-box__plus\"\n\t\t    @tap.stop=\"clickHandler('plus')\"\n\t\t    @touchstart=\"onTouchStart('plus')\"\n\t\t    @touchend.stop=\"clearTimeout\"\n\t\t    hover-class=\"u-number-box__plus--hover\"\n\t\t    hover-stay-time=\"150\"\n\t\t    :class=\"{ 'u-number-box__minus--disabled': isDisabled('plus') }\"\n\t\t    :style=\"[buttonStyle('plus')]\"\n\t\t>\n\t\t\t<u-icon\n\t\t\t    name=\"plus\"\n\t\t\t    :color=\"isDisabled('plus') ? '#c8c9cc' : '#323233'\"\n\t\t\t    size=\"15\"\n\t\t\t    bold\n\t\t\t\t:customStyle=\"iconStyle\"\n\t\t\t></u-icon>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport props from './props.js';\n\t/**\n\t * numberBox 步进器\n\t * @description 该组件一般用于商城购物选择物品数量的场景。\n\t * @tutorial https://uviewui.com/components/numberBox.html\n\t * @property {String | Number}\tname\t\t\t步进器标识符，在change回调返回\n\t * @property {String | Number}\tvalue\t\t\t用于双向绑定的值，初始化时设置设为默认min值(最小值)  （默认 0 ）\n\t * @property {String | Number}\tmin\t\t\t\t最小值 （默认 1 ）\n\t * @property {String | Number}\tmax\t\t\t\t最大值 （默认 Number.MAX_SAFE_INTEGER ）\n\t * @property {String | Number}\tstep\t\t\t加减的步长，可为小数 （默认 1 ）\n\t * @property {Boolean}\t\t\tinteger\t\t\t是否只允许输入整数 （默认 false ）\n\t * @property {Boolean}\t\t\tdisabled\t\t是否禁用，包括输入框，加减按钮 （默认 false ）\n\t * @property {Boolean}\t\t\tdisabledInput\t是否禁用输入框 （默认 false ）\n\t * @property {Boolean}\t\t\tasyncChange\t\t是否开启异步变更，开启后需要手动控制输入值 （默认 false ）\n\t * @property {String | Number}\tinputWidth\t\t输入框宽度，单位为px （默认 35 ）\n\t * @property {Boolean}\t\t\tshowMinus\t\t是否显示减少按钮 （默认 true ）\n\t * @property {Boolean}\t\t\tshowPlus\t\t是否显示增加按钮 （默认 true ）\n\t * @property {String | Number}\tdecimalLength\t显示的小数位数\n\t * @property {Boolean}\t\t\tlongPress\t\t是否开启长按加减手势 （默认 true ）\n\t * @property {String}\t\t\tcolor\t\t\t输入框文字和加减按钮图标的颜色 （默认 '#323233' ）\n\t * @property {String | Number}\tbuttonSize\t\t按钮大小，宽高等于此值，单位px，输入框高度和此值保持一致 （默认 30 ）\n\t * @property {String}\t\t\tbgColor\t\t\t输入框和按钮的背景颜色 （默认 '#EBECEE' ）\n\t * @property {String | Number}\tcursorSpacing\t指定光标于键盘的距离，避免键盘遮挡输入框，单位px （默认 100 ）\n\t * @property {Boolean}\t\t\tdisablePlus\t\t是否禁用增加按钮 （默认 false ）\n\t * @property {Boolean}\t\t\tdisableMinus\t是否禁用减少按钮 （默认 false ）\n\t * @property {Object ｜ String}\ticonStyle\t\t加减按钮图标的样式\n\t *\n\t * @event {Function}\tonFocus\t输入框活动焦点\n\t * @event {Function}\tonBlur\t输入框失去焦点\n\t * @event {Function}\tonInput\t输入框值发生变化\n\t * @event {Function}\tonChange\n\t * @example <u-number-box v-model=\"value\" @change=\"valChange\"></u-number-box>\n\t */\n\texport default {\n\t\tname: 'u-number-box',\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 输入框实际操作的值\n\t\t\t\tcurrentValue: '',\n\t\t\t\t// 定时器\n\t\t\t\tlongPressTimer: null\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\t// 多个值之间，只要一个值发生变化，都要重新检查check()函数\n\t\t\twatchChange(n) {\n\t\t\t\tthis.check()\n\t\t\t},\n\t\t\t// 监听v-mode的变化，重新初始化内部的值\n\t\t\tvalue(n) {\n\t\t\t\tif (n !== this.currentValue) {\n\t\t\t\t\tthis.currentValue = this.format(this.value)\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tgetCursorSpacing() {\n\t\t\t\t// 判断传入的单位，如果为px单位，需要转成px\n\t\t\t\treturn uni.$u.getPx(this.cursorSpacing)\n\t\t\t},\n\t\t\t// 按钮的样式\n\t\t\tbuttonStyle() {\n\t\t\t\treturn (type) => {\n\t\t\t\t\tconst style = {\n\t\t\t\t\t\tbackgroundColor: this.bgColor,\n\t\t\t\t\t\theight: uni.$u.addUnit(this.buttonSize),\n\t\t\t\t\t\tcolor: this.color\n\t\t\t\t\t}\n\t\t\t\t\tif (this.isDisabled(type)) {\n\t\t\t\t\t\tstyle.backgroundColor = '#f7f8fa'\n\t\t\t\t\t}\n\t\t\t\t\treturn style\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 输入框的样式\n\t\t\tinputStyle() {\n\t\t\t\tconst disabled = this.disabled || this.disabledInput\n\t\t\t\tconst style = {\n\t\t\t\t\tcolor: this.color,\n\t\t\t\t\tbackgroundColor: this.bgColor,\n\t\t\t\t\theight: uni.$u.addUnit(this.buttonSize),\n\t\t\t\t\twidth: uni.$u.addUnit(this.inputWidth)\n\t\t\t\t}\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 用于监听多个值发生变化\n\t\t\twatchChange() {\n\t\t\t\treturn [this.integer, this.decimalLength, this.min, this.max]\n\t\t\t},\n\t\t\tisDisabled() {\n\t\t\t\treturn (type) => {\n\t\t\t\t\tif (type === 'plus') {\n\t\t\t\t\t\t// 在点击增加按钮情况下，判断整体的disabled，是否单独禁用增加按钮，以及当前值是否大于最大的允许值\n\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\tthis.disabled ||\n\t\t\t\t\t\t\tthis.disablePlus ||\n\t\t\t\t\t\t\tthis.currentValue >= this.max\n\t\t\t\t\t\t)\n\t\t\t\t\t}\n\t\t\t\t\t// 点击减少按钮同理\n\t\t\t\t\treturn (\n\t\t\t\t\t\tthis.disabled ||\n\t\t\t\t\t\tthis.disableMinus ||\n\t\t\t\t\t\tthis.currentValue <= this.min\n\t\t\t\t\t)\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t\tmounted() {\n\t\t\tthis.init()\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\tthis.currentValue = this.format(this.value)\n\t\t\t},\n\t\t\t// 格式化整理数据，限制范围\n\t\t\tformat(value) {\n\t\t\t\tvalue = this.filter(value)\n\t\t\t\t// 如果为空字符串，那么设置为0，同时将值转为Number类型\n\t\t\t\tvalue = value === '' ? 0 : +value\n\t\t\t\t// 对比最大最小值，取在min和max之间的值\n\t\t\t\tvalue = Math.max(Math.min(this.max, value), this.min)\n\t\t\t\t// 如果设定了最大的小数位数，使用toFixed去进行格式化\n\t\t\t\tif (this.decimalLength !== null) {\n\t\t\t\t\tvalue = value.toFixed(this.decimalLength)\n\t\t\t\t}\n\t\t\t\treturn value\n\t\t\t},\n\t\t\t// 过滤非法的字符\n\t\t\tfilter(value) {\n\t\t\t\t// 只允许0-9之间的数字，\".\"为小数点，\"-\"为负数时候使用\n\t\t\t\tvalue = String(value).replace(/[^0-9.-]/g, '')\n\t\t\t\t// 如果只允许输入整数，则过滤掉小数点后的部分\n\t\t\t\tif (this.integer && value.indexOf('.') !== -1) {\n\t\t\t\t\tvalue = value.split('.')[0]\n\t\t\t\t}\n\t\t\t\treturn value;\n\t\t\t},\n\t\t\tcheck() {\n\t\t\t\t// 格式化了之后，如果前后的值不相等，那么设置为格式化后的值\n\t\t\t\tconst val = this.format(this.currentValue);\n\t\t\t\tif (val !== this.currentValue) {\n\t\t\t\t\tthis.currentValue = val\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 判断是否出于禁止操作状态\n\t\t\t// isDisabled(type) {\n\t\t\t// \tif (type === 'plus') {\n\t\t\t// \t\t// 在点击增加按钮情况下，判断整体的disabled，是否单独禁用增加按钮，以及当前值是否大于最大的允许值\n\t\t\t// \t\treturn (\n\t\t\t// \t\t\tthis.disabled ||\n\t\t\t// \t\t\tthis.disablePlus ||\n\t\t\t// \t\t\tthis.currentValue >= this.max\n\t\t\t// \t\t)\n\t\t\t// \t}\n\t\t\t// \t// 点击减少按钮同理\n\t\t\t// \treturn (\n\t\t\t// \t\tthis.disabled ||\n\t\t\t// \t\tthis.disableMinus ||\n\t\t\t// \t\tthis.currentValue <= this.min\n\t\t\t// \t)\n\t\t\t// },\n\t\t\t// 输入框活动焦点\n\t\t\tonFocus(event) {\n\t\t\t\tthis.$emit('focus', {\n\t\t\t\t\t...event.detail,\n\t\t\t\t\tname: this.name,\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 输入框失去焦点\n\t\t\tonBlur(event) {\n\t\t\t\t// 对输入值进行格式化\n\t\t\t\tconst value = this.format(event.detail.value)\n\t\t\t\t// 发出blur事件\n\t\t\t\tthis.$emit(\n\t\t\t\t\t'blur',{\n\t\t\t\t\t\t...event.detail,\n\t\t\t\t\t\tname: this.name,\n\t\t\t\t\t}\n\t\t\t\t)\n\t\t\t},\n\t\t\t// 输入框值发生变化\n\t\t\tonInput(e) {\n\t\t\t\tconst {\n\t\t\t\t\tvalue = ''\n\t\t\t\t} = e.detail || {}\n\t\t\t\t// 为空返回\n\t\t\t\tif (value === '') return\n\t\t\t\tlet formatted = this.filter(value)\n\t\t\t\t// 最大允许的小数长度\n\t\t\t\tif (this.decimalLength !== null && formatted.indexOf('.') !== -1) {\n\t\t\t\t\tconst pair = formatted.split('.');\n\t\t\t\t\tformatted = `${pair[0]}.${pair[1].slice(0, this.decimalLength)}`\n\t\t\t\t}\n\t\t\t\tformatted = this.format(formatted)\n\t\t\t\tthis.emitChange(formatted);\n\t\t\t},\n\t\t\t// 发出change事件\n\t\t\temitChange(value) {\n\t\t\t\t// 如果开启了异步变更值，则不修改内部的值，需要用户手动在外部通过v-model变更\n\t\t\t\tif (!this.asyncChange) {\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.$emit('input', value)\n\t\t\t\t\t\tthis.currentValue = value\n\t\t\t\t\t\tthis.$forceUpdate()\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\tthis.$emit('change', {\n\t\t\t\t\tvalue,\n\t\t\t\t\tname: this.name,\n\t\t\t\t});\n\t\t\t},\n\t\t\tonChange() {\n\t\t\t\tconst {\n\t\t\t\t\ttype\n\t\t\t\t} = this\n\t\t\t\tif (this.isDisabled(type)) {\n\t\t\t\t\treturn this.$emit('overlimit', type)\n\t\t\t\t}\n\t\t\t\tconst diff = type === 'minus' ? -this.step : +this.step\n\t\t\t\tconst value = this.format(this.add(+this.currentValue, diff))\n\t\t\t\tthis.emitChange(value)\n\t\t\t\tthis.$emit(type)\n\t\t\t},\n\t\t\t// 对值扩大后进行四舍五入，再除以扩大因子，避免出现浮点数操作的精度问题\n\t\t\tadd(num1, num2) {\n\t\t\t\tconst cardinal = Math.pow(10, 10);\n\t\t\t\treturn Math.round((num1 + num2) * cardinal) / cardinal\n\t\t\t},\n\t\t\t// 点击加减按钮\n\t\t\tclickHandler(type) {\n\t\t\t\tthis.type = type\n\t\t\t\tthis.onChange()\n\t\t\t},\n\t\t\tlongPressStep() {\n\t\t\t\t// 每隔一段时间，重新调用longPressStep方法，实现长按加减\n\t\t\t\tthis.clearTimeout()\n\t\t\t\tthis.longPressTimer = setTimeout(() => {\n\t\t\t\t\tthis.onChange()\n\t\t\t\t\tthis.longPressStep()\n\t\t\t\t}, 250);\n\t\t\t},\n\t\t\tonTouchStart(type) {\n\t\t\t\tif (!this.longPress) return\n\t\t\t\tthis.clearTimeout()\n\t\t\t\tthis.type = type\n\t\t\t\t// 一定时间后，默认达到长按状态\n\t\t\t\tthis.longPressTimer = setTimeout(() => {\n\t\t\t\t\tthis.onChange()\n\t\t\t\t\tthis.longPressStep()\n\t\t\t\t}, 600)\n\t\t\t},\n\t\t\t// 触摸结束，清除定时器，停止长按加减\n\t\t\tonTouchEnd() {\n\t\t\t\tif (!this.longPress) return\n\t\t\t\tthis.clearTimeout()\n\t\t\t},\n\t\t\t// 清除定时器\n\t\t\tclearTimeout() {\n\t\t\t\tclearTimeout(this.longPressTimer)\n\t\t\t\tthis.longPressTimer = null\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import '../../libs/css/components.scss';\n\n\t$u-numberBox-hover-bgColor: #E6E6E6 !default;\n\t$u-numberBox-disabled-color: #c8c9cc !default;\n\t$u-numberBox-disabled-bgColor: #f7f8fa !default;\n\t$u-numberBox-plus-radius: 4px !default;\n\t$u-numberBox-minus-radius: 4px !default;\n\t$u-numberBox-input-text-align: center !default;\n\t$u-numberBox-input-font-size: 15px !default;\n\t$u-numberBox-input-padding: 0 !default;\n\t$u-numberBox-input-margin: 0 2px !default;\n\t$u-numberBox-input-disabled-color: #c8c9cc !default;\n\t$u-numberBox-input-disabled-bgColor: #f2f3f5 !default;\n\n\t.u-number-box {\n\t\t@include flex(row);\n\t\talign-items: center;\n\n\t\t&__slot {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\ttouch-action: none;\n\t\t\t/* #endif */\n\t\t}\n\n\t\t&__plus,\n\t\t&__minus {\n\t\t\twidth: 35px;\n\t\t\t@include flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\ttouch-action: none;\n\t\t\t/* #endif */\n\n\t\t\t&--hover {\n\t\t\t\tbackground-color: $u-numberBox-hover-bgColor !important;\n\t\t\t}\n\n\t\t\t&--disabled {\n\t\t\t\tcolor: $u-numberBox-disabled-color;\n\t\t\t\tbackground-color: $u-numberBox-disabled-bgColor;\n\t\t\t}\n\t\t}\n\n\t\t&__plus {\n\t\t\tborder-top-right-radius: $u-numberBox-plus-radius;\n\t\t\tborder-bottom-right-radius: $u-numberBox-plus-radius;\n\t\t}\n\n\t\t&__minus {\n\t\t\tborder-top-left-radius: $u-numberBox-minus-radius;\n\t\t\tborder-bottom-left-radius: $u-numberBox-minus-radius;\n\t\t}\n\n\t\t&__input {\n\t\t\tposition: relative;\n\t\t\ttext-align: $u-numberBox-input-text-align;\n\t\t\tfont-size: $u-numberBox-input-font-size;\n\t\t\tpadding: $u-numberBox-input-padding;\n\t\t\tmargin: $u-numberBox-input-margin;\n\t\t\t@include flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\n\t\t\t&--disabled {\n\t\t\t\tcolor: $u-numberBox-input-disabled-color;\n\t\t\t\tbackground-color: $u-numberBox-input-disabled-bgColor;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-number-box.vue?vue&type=style&index=0&id=18418972&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-number-box.vue?vue&type=style&index=0&id=18418972&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755661607393\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}