{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?8b12", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?09a4", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?6c29", "uni-app:///shifu/master_my_order.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?4254", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?8995"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Upload", "data", "limit", "coachId", "shifuId", "tmplIds", "status", "list", "name", "value", "currentIndex", "page", "orderList", "pay_typeArr", "isLoading", "showDiffApply", "currentOrderItemForDiff", "diffApplyForm", "diffAmount", "reasonType", "reasonDetail", "partsimgs", "diffApplyRules", "required", "message", "trigger", "validator", "onReady", "onReachBottom", "onPullDownRefresh", "methods", "getDiffStatusText", "formatWarrantyDate", "console", "getScrollViewHeight", "showDiffApplyModal", "imgUploadDiff", "imgtype", "closeDiffApplyModal", "diffApplyConfirm", "uni", "icon", "title", "partsImgsString", "orderId", "partsImgs", "res", "showDiffCancelModal", "diffItem", "content", "confirmText", "cancelText", "success", "diffCancel", "cancelId", "id", "cancellModal", "response", "dingyue", "templateId", "templateCategoryId", "selectedTmplIds", "fail", "loadMore", "refreshList", "setTimeout", "fetchOrders", "payType", "pageNum", "pageSize", "filter", "map", "item", "orderDiffPriceList", "goDetail", "url", "goUrl", "showConfirmModal", "startFu", "queren", "updateHigh", "shiInfoid", "userId", "role", "getList", "handleHeader", "tab", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAA81B,CAAgB,82BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsJl3B;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC,UACA,gDACA,+CACA,+CACA,8CACA;MACAC;MACAC;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;MACAC;MAAA;MACAC;QACAC;QACAC;QAAA;QACAC;QACAC;MACA;;MACAC;QACAJ;UACAK;UACAC;UACAC;QACA;UACAC;YACA;UACA;UACAF;UACAC;QACA;QACAL;UACAG;UACAC;UACAC;QACA;MACA;IACA;EACA;EACAE;IACA;EAAA,CACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;QACA;QACA;UACA;QACA;QACA;QACA;QACA;QACA;MACA;QACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;QAAA;QACAjB;QACAC;QAAA;QACAC;QACAC;MACA;;MACA;;MAEA;MACA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAe;MACAH;MACA;QAAAI;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACApB;QACAC;QACAC;QACAC;MACA;IACA;IAEAkB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKA;gBACAC;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACA1B;kBACAC;kBAAA;kBACAC;kBACAyB;gBACA;cAAA;gBANAC;gBAOA;kBACAN;oBACAE;oBACAD;kBACA;kBACA;kBACA;gBACA;kBACAD;oBACAE;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;kBACAE;kBACAD;gBACA;gBACAR;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBACAO;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAK;MAAA;MACA;MACA,0FACAC,wDACA;MAEAR;QACAE;QACAO;QACAC;QACAC;QACAC;UACA;YACA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEApB;gBACA;gBACAqB;gBAAA;gBAAA,OACA;kBACAC;gBACA;cAAA;gBAFAT;gBAGA;kBACAN;oBACAE;oBACAD;kBACA;kBACA;gBACA;kBACAD;oBACAE;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;kBACAE;kBACAD;gBACA;gBACAR;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAuB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAhB;kBACAE;kBACAO;kBACAC;kBACAC;kBACAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAN;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA;8BAAA,OAEA;gCACAS;8BACA;4BAAA;8BAFAE;8BAIA;gCACAjB;kCACAC;kCACAC;gCACA;gCACA;8BACA;gCACAF;kCACAC;kCACAC;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEAT;8BACAO;gCACAC;gCACAC;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAgB;MAAA;MACAzB;MACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;MACAA;MACA;QAAA;UACA0B;UACAC;QACA;MAAA;MACApB;QACAnC;QACA+C;UACAnB;UACA;UACA;UACA4B;YACA5B;YACA;cACA;cACA;gBACA;kBACA;gBACA;cACA;cACA;cACA;cACA;cACA;cACAA;YACA;UACA;UACAA;QACA;QACA6B;UACA7B;QACA;MACA;IACA;IAEA8B;MACA;MACA;MACA;MAEA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACAC;QACAzB;MACA;IACA;IAEA0B;MAAA;MAAA;MACA;QACA/D;QACAgE;QACAC;QACAC;MACA;QACA;UACA7B;YACAC;YACAC;UACA;UACA;QACA;UACA;UACA,0BACA4B;YAAA;UAAA,GACAC;YAAA,uCACAC;cACAL;cACA;cACAM;YAAA;UAAA,CACA;UAEA;YACA;UACA;YACA;UACA;UAEA;UACA;QACA;QACA;QACA;MACA;QACA;QACA;QACAjC;UACAE;UACAD;QACA;QACAR;QACA;MACA;IACA;IAEAyC;MACAlC;MACAA;QACAmC;MACA;IACA;IAEAC;MACApC;QACAmC;MACA;IACA;IAEAE;MAAA;MACArC;QACAE;QACAO;QACAC;QACAC;QACAC;UACA;YACA;cACA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA0B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAvB;kBACAY;gBACA;cAAA;gBAHArB;gBAIA;kBACAN;oBACAE;oBACAD;kBACA;kBACA;gBACA;kBACAD;oBACAE;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;kBACAE;kBACAD;gBACA;gBACAR;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA8C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAxB;kBACAY;gBACA;cAAA;gBAHArB;gBAIA;kBACAN;oBACAE;oBACAD;kBACA;kBACA;gBACA;kBACAD;oBACAE;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;kBACAE;kBACAD;gBACA;gBACAR;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA+C;MACA;MACA;QACA/C;QACA;MACA;MACA;MACA;QACAgD;MACA;QACAhD;QACA;MACA;MACA;QACAiD;QACAC;QACAhB;MACA;QACAlC;MACA;QACAA;MACA;IACA;IAEAmD;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;MACAtD;MACA;IACA;IAEA;MACA;IACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7sBA;AAAA;AAAA;AAAA;AAAqmD,CAAgB,yjDAAG,EAAC,C;;;;;;;;;;;ACAznD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/master_my_order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/master_my_order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./master_my_order.vue?vue&type=template&id=b530dfe4&scoped=true&\"\nvar renderjs\nimport script from \"./master_my_order.vue?vue&type=script&lang=js&\"\nexport * from \"./master_my_order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./master_my_order.vue?vue&type=style&index=0&id=b530dfe4&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b530dfe4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/master_my_order.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=template&id=b530dfe4&scoped=true&\"", "var render = function () {}\nvar staticRenderFns = []\nvar recyclableRender\nvar components\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header_item\" v-for=\"(item, index) in list\" :key=\"index\" @click=\"handleHeader(item)\">\n\t\t\t\t<view :style=\"currentIndex == item.value ? 'color:#2E80FE;' : ''\">{{ item.name }}</view>\n\t\t\t\t<view class=\"blue\" :style=\"currentIndex == item.value ? '' : 'background-color:#fff;'\"></view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view @click=\"dingyue()\" class=\"main\">\n\t\t\t<!-- 主订单 -->\n\t\t\t<view class=\"main_item\" v-for=\"(item, index) in orderList\" :key=\"index\" @click=\"goDetail(item)\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<view class=\"no\">单号：{{ item.orderCode }}</view>\n\t\t\t\t\t<view class=\"type\">{{ item.payType == -1 ? '已取消' : pay_typeArr[item.payType] }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-type-info\">\n\t\t\t\t\t<view class=\"order-type-row\">\n\t\t\t\t\t\t<text class=\"order-type-label\">报价类型：</text>\n\t\t\t\t\t\t<text class=\"order-type-value\">{{ item.type === 0 ? '一口价模式' : item.type === 1 ? '报价模式' : '未知类型'\n\t\t\t\t\t\t}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"urgent-indicator\" v-if=\"item.urgent !== 0\">\n\t\t\t\t\t\t<text class=\"urgent-text\">高价值</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"mid\">\n\t\t\t\t\t<view class=\"lef\">\n\t\t\t\t\t\t<image :src=\"item.goodsCover\" mode=\"\"></image>\n\t\t\t\t\t\t<text>{{ item.goodsName }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"righ\" v-if=\"item.payType == 7 || (item.payType == 7 && item.isAftermarket === 1)\">\n\t\t\t\t\t\t<view>￥{{ item.coachServicePrice }}</view>\n\t\t\t\t\t\t<view>x{{ item.num }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"bot\">\n\t\t\t\t\t<view class=\"qzf\" v-if=\"item.payType === 3 || item.payType === 5\"\n\t\t\t\t\t\************=\"showDiffApplyModal(item)\">\n\t\t\t\t\t\t差价申请\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"qzf\" v-if=\"item.payType >= 0 && item.payType < 7\" @click.stop=\"cancellModal(item)\">\n\t\t\t\t\t\t取消接单\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"qzf\" v-if=\"item.payType === 3\" @click.stop=\"showConfirmModal(item, 'queren')\">\n\t\t\t\t\t\t确认到达\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"qzf\" v-if=\"item.payType === 5\" @click.stop=\"showConfirmModal(item, 'startFu')\">\n\t\t\t\t\t\t开始服务\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 子订单（差价申请列表） -->\n\t\t\t\t<view v-if=\"item.orderDiffPriceList && item.orderDiffPriceList.length > 0\" class=\"sub_orders\">\n\t\t\t\t\t<view class=\"sub_title\">差价申请记录</view>\n\t\t\t\t\t<scroll-view class=\"sub_scroll_container\" scroll-y=\"true\" :style=\"getScrollViewHeight(item.orderDiffPriceList.length)\">\n\t\t\t\t\t\t<view class=\"sub_item\" v-for=\"(diffItem, diffIndex) in item.orderDiffPriceList\" :key=\"diffItem.id\"\n\t\t\t\t\t\t\************=\"\">\n\t\t\t\t\t\t\t<view class=\"sub_head\">\n\t\t\t\t\t\t\t\t<view class=\"sub_no\">差价单号：{{ diffItem.diffCode }}</view>\n\t\t\t\t\t\t\t\t<view class=\"sub_status\">{{ getDiffStatusText(diffItem.status) }}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"sub_content\">\n\t\t\t\t\t\t\t\t<view class=\"sub_info_grid\">\n\t\t\t\t\t\t\t\t\t<view class=\"sub_info_row\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"sub_info_item\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"sub_label\">差价金额：</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"sub_value sub_amount_value\">￥{{ diffItem.diffAmount }}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"sub_info_item\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"sub_label\">质保日期：</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"sub_value sub_warranty_value\">{{ formatWarrantyDate(diffItem.partsWarrantyPeriod) }}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"sub_info_row\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"sub_info_item sub_reason_item\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"sub_label\">原因：</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"sub_value sub_reason_value\">{{ diffItem.reasonDetail }}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"sub_info_row\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"sub_info_item\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"sub_label\">申请时间：</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"sub_value sub_time_value\">{{ diffItem.createdTime }}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"sub_actions\">\n\t\t\t\t\t\t\t\t\t<view class=\"sub_qzf\" v-if=\"diffItem.status === 0\"\n\t\t\t\t\t\t\t\t\t\************=\"showDiffCancelModal(item, diffItem)\">\n\t\t\t\t\t\t\t\t\t\t取消差价\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<u-loadmore :status=\"status\" @loadmore=\"loadMore\" />\n\n\t\t<!-- 自定义差价申请弹窗 -->\n\t\t<view class=\"diff-apply-modal\" v-if=\"showDiffApply\" @click=\"closeDiffApplyModal\">\n\t\t\t<view class=\"modal-content\" @click.stop=\"\">\n\t\t\t\t<!-- 弹窗头部 -->\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<view class=\"modal-title\">差价申请</view>\n\t\t\t\t\t<view class=\"close-btn\" @click=\"closeDiffApplyModal\">\n\t\t\t\t\t\t<text class=\"close-icon\">×</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 弹窗内容 -->\n\t\t\t\t<view class=\"modal-body\">\n\t\t\t\t\t<u--form labelPosition=\"left\" :model=\"diffApplyForm\" :rules=\"diffApplyRules\" ref=\"diffApplyForm\">\n\t\t\t\t\t\t<u-form-item label=\"差价金额\" prop=\"diffAmount\" borderBottom ref=\"item1\">\n\t\t\t\t\t\t\t<u--input v-model=\"diffApplyForm.diffAmount\" placeholder=\"请输入差价金额\" type=\"number\"\n\t\t\t\t\t\t\t\tborder=\"none\"></u--input>\n\t\t\t\t\t\t</u-form-item>\n\t\t\t\t\t\t<u-form-item label=\"差价原因\" borderBottom>\n\t\t\t\t\t\t\t<view class=\"reason-type-display\">\n\t\t\t\t\t\t\t\t<text class=\"reason-type-text\">配件不符合</text>\n\t\t\t\t\t\t\t\t<view class=\"reason-type-badge\">类型: 1</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</u-form-item>\n\t\t\t\t\t\t<u-form-item label=\"原因详情\" prop=\"reasonDetail\" borderBottom ref=\"item3\">\n\t\t\t\t\t\t\t<u--textarea v-model=\"diffApplyForm.reasonDetail\" placeholder=\"请输入差价原因详情\"\n\t\t\t\t\t\t\t\tcount></u--textarea>\n\t\t\t\t\t\t</u-form-item>\n\t\t\t\t\t\t<u-form-item label=\"配件图\" borderBottom ref=\"item4\">\n\t\t\t\t\t\t\t<view class=\"upload-container\">\n\t\t\t\t\t\t\t\t<upload @upload=\"imgUploadDiff\" @del=\"imgUploadDiff\"\n\t\t\t\t\t\t\t\t\t:imagelist=\"diffApplyForm.partsimgs\" imgtype=\"partsimgs\" imgclass=\"parts-img\"\n\t\t\t\t\t\t\t\t\ttext=\"上传配件图\" :imgsize=\"9\"></upload>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</u-form-item>\n\t\t\t\t\t</u--form>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 弹窗底部按钮 -->\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<view class=\"btn-cancel\" @click=\"closeDiffApplyModal\">取消</view>\n\t\t\t\t\t<view class=\"btn-confirm\" @click=\"diffApplyConfirm\">确认申请</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport Upload from '@/components/upload.vue'; // Import upload component\nexport default {\n\tcomponents: {\n\t\tUpload,\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tlimit: 10,\n\t\t\tcoachId: '',\n\t\t\tshifuId: '',\n\t\t\ttmplIds: [\n\t\t\t\t' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo',\n\t\t\t\t'9sT8DPghuzkjRmg3gBefKWgrZHMIkJs0l7hZKgL5SWY',\n\t\t\t\t'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs',\n\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t],\n\t\t\tstatus: 'loadmore',\n\t\t\tlist: [{\n\t\t\t\tname: '全部',\n\t\t\t\tvalue: 0\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: '待上门',\n\t\t\t\tvalue: 3\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: '待服务',\n\t\t\t\tvalue: 5\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: '服务中',\n\t\t\t\tvalue: 6\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: '已完成',\n\t\t\t\tvalue: 7\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: '售后',\n\t\t\t\tvalue: 8\n\t\t\t},\n\t\t\t],\n\t\t\tcurrentIndex: 0,\n\t\t\tpage: 0,\n\t\t\torderList: [],\n\t\t\tpay_typeArr: ['', '待支付', '已支付', '已接单', '上门中', '待服务', '服务中', '已完成', '售后'],\n\t\t\tisLoading: false, // Flag to prevent multiple API calls\n\n\t\t\t// For diffapply modal\n\t\t\tshowDiffApply: false,\n\t\t\tcurrentOrderItemForDiff: null, // To store the item for which diff apply is initiated\n\t\t\tdiffApplyForm: {\n\t\t\t\tdiffAmount: '',\n\t\t\t\treasonType: 1, // 差价原因类型，目前固定为1代表配件不符合\n\t\t\t\treasonDetail: '',\n\t\t\t\tpartsimgs: [], // 配件图片\n\t\t\t},\n\t\t\tdiffApplyRules: {\n\t\t\t\tdiffAmount: [{\n\t\t\t\t\trequired: true,\n\t\t\t\t\tmessage: '请输入差价金额',\n\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t}, {\n\t\t\t\t\tvalidator: (rule, value, callback) => {\n\t\t\t\t\t\treturn value >= 0.01;\n\t\t\t\t\t},\n\t\t\t\t\tmessage: '差价金额必须大于等于0.01',\n\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t}],\n\t\t\t\treasonDetail: [{\n\t\t\t\t\trequired: true,\n\t\t\t\t\tmessage: '请输入差价原因详情',\n\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t}],\n\t\t\t}\n\t\t};\n\t},\n\tonReady() {\n\t\t// 由于弹窗默认隐藏，表单引用可能不存在，所以在显示弹窗时再设置规则\n\t},\n\tonReachBottom() {\n\t\tthis.loadMore();\n\t},\n\tonPullDownRefresh() {\n\t\tthis.refreshList();\n\t},\n\tmethods: {\n\t\t// 获取差价申请状态文本\n\t\tgetDiffStatusText(status) {\n\t\t\tconst statusMap = {\n\t\t\t\t'-1': '已取消',\n\t\t\t\t0: '待确认',\n\t\t\t\t1: '已确认待支付',\n\t\t\t\t2: '已支付',\n\t\t\t\t3: '已拒绝'\n\t\t\t};\n\t\t\treturn statusMap[status] || '未知状态';\n\t\t},\n\n\t\t// 格式化配件质保日期\n\t\tformatWarrantyDate(timestamp) {\n\t\t\tif (!timestamp) {\n\t\t\t\treturn '无质保信息';\n\t\t\t}\n\t\t\ttry {\n\t\t\t\tconst date = new Date(timestamp);\n\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\treturn '无效日期';\n\t\t\t\t}\n\t\t\t\tconst year = date.getFullYear();\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\t\treturn `${year}-${month}-${day}`;\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('格式化质保日期失败:', error);\n\t\t\t\treturn '格式错误';\n\t\t\t}\n\t\t},\n\n\t\t// 获取滚动视图高度\n\t\tgetScrollViewHeight(itemCount) {\n\t\t\t// 每个项目大约150rpx高度，最多显示3个项目，超过则可滚动\n\t\t\tconst maxHeight = 450; // 3 * 150rpx\n\t\t\tconst itemHeight = 150;\n\t\t\tconst calculatedHeight = Math.min(itemCount * itemHeight, maxHeight);\n\t\t\treturn `height: ${calculatedHeight}rpx;`;\n\t\t},\n\n\t\tshowDiffApplyModal(item) {\n\t\t\tthis.currentOrderItemForDiff = item;\n\t\t\tthis.diffApplyForm = { // Reset form for new application\n\t\t\t\tdiffAmount: '',\n\t\t\t\treasonType: 1, // 差价原因类型，目前固定为1代表配件不符合\n\t\t\t\treasonDetail: '',\n\t\t\t\tpartsimgs: [], // 配件图片\n\t\t\t};\n\t\t\tthis.showDiffApply = true;\n\n\t\t\t// 在下一个tick中设置表单规则，确保DOM已渲染\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tif (this.$refs.diffApplyForm && this.$refs.diffApplyForm.setRules) {\n\t\t\t\t\tthis.$refs.diffApplyForm.setRules(this.diffApplyRules);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 处理差价申请中的图片上传\n\t\timgUploadDiff(e) {\n\t\t\tconsole.log('imgUploadDiff event:', e);\n\t\t\tconst { imagelist, imgtype } = e;\n\t\t\tthis.$set(this.diffApplyForm, imgtype, imagelist);\n\t\t},\n\n\t\t// 关闭差价申请弹窗\n\t\tcloseDiffApplyModal() {\n\t\t\tthis.showDiffApply = false;\n\t\t\t// 重置表单数据\n\t\t\tthis.diffApplyForm = {\n\t\t\t\tdiffAmount: '',\n\t\t\t\treasonType: 1,\n\t\t\t\treasonDetail: '',\n\t\t\t\tpartsimgs: [],\n\t\t\t};\n\t\t},\n\n\t\tasync diffApplyConfirm() {\n\t\t\t// Validate the form before submitting\n\t\t\ttry {\n\t\t\t\t// 检查表单引用是否存在\n\t\t\t\tif (!this.$refs.diffApplyForm || !this.$refs.diffApplyForm.validate) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '表单未准备就绪，请稍后重试'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tawait this.$refs.diffApplyForm.validate();\n\t\t\t\t// Form is valid, proceed with API call\n\t\t\t\tif (!this.currentOrderItemForDiff) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '订单信息缺失'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\t// 准备配件图片数据 - 根据API文档，partsImgs是string类型，需要转换\n\t\t\t\t\tconst partsImgsString = this.diffApplyForm.partsimgs.map(img => img.path).join(',');\n\n\t\t\t\t\tconst res = await this.$api.shifu.diffApply({\n\t\t\t\t\t\torderId: this.currentOrderItemForDiff.id,\n\t\t\t\t\t\tdiffAmount: parseFloat(this.diffApplyForm.diffAmount),\n\t\t\t\t\t\treasonType: this.diffApplyForm.reasonType, // 差价原因类型(1配件不符合)\n\t\t\t\t\t\treasonDetail: this.diffApplyForm.reasonDetail,\n\t\t\t\t\t\tpartsImgs: partsImgsString // 配件图片，string类型，多个图片用逗号分隔\n\t\t\t\t\t});\n\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '差价申请成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.showDiffApply = false;\n\t\t\t\t\t\tthis.refreshList(); // Refresh list to reflect changes\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg || '差价申请失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tconsole.error('Error in diffApply:', err);\n\t\t\t\t}\n\t\t\t} catch (errors) {\n\t\t\t\tconsole.error('Form validation failed:', errors);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '请检查填写信息'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\tshowDiffCancelModal(item, diffItem) {\n\t\t\tconst title = diffItem ? '取消差价申请' : '取消差价申请';\n\t\t\tconst content = diffItem ?\n\t\t\t\t`确定要取消差价单号 ${diffItem.diffCode} 的申请吗？` :\n\t\t\t\t'确定要取消此订单的差价申请吗？';\n\n\t\t\tuni.showModal({\n\t\t\t\ttitle: title,\n\t\t\t\tcontent: content,\n\t\t\t\tconfirmText: '确定',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthis.diffCancel(item, diffItem);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\tasync diffCancel(item, diffItem) {\n\t\t\ttry {\n\t\t\t\tconsole.log(diffItem)\n\t\t\t\t// 如果有具体的差价申请项，使用差价申请的ID，否则使用订单ID\n\t\t\t\tconst cancelId = diffItem.id\n\t\t\t\tconst res = await this.$api.shifu.diffCancel({\n\t\t\t\t\tid: cancelId\n\t\t\t\t});\n\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '差价申请已取消',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.refreshList();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg || '取消失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tconsole.error('Error in diffCancel:', err);\n\t\t\t}\n\t\t},\n\n\t\tasync cancellModal(item) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '取消订单',\n\t\t\t\tcontent: '确定要取消此订单吗？',\n\t\t\t\tconfirmText: '确定',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst response = await this.$api.shifu.orderCancel({\n\t\t\t\t\t\t\t\tid: item.id\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tif (response.code === \"200\") {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\ttitle: response.msg || '订单已取消'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tthis.getList();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\ttitle: response.msg || '取消失败，请重试'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\t\tconsole.error('Error cancelling order:', err);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\ttitle: err.message || '网络请求失败，请检查网络连接'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\tdingyue() {\n\t\t\tconsole.log('dingyue called');\n\t\t\tconst allTmplIds = this.tmplIds;\n\t\t\tconst requiredTmplId = '9sT8DPghuzkjRmg3gBefKWgrZHMIkJs0l7hZKgL5SWY';\n\t\t\tif (allTmplIds.length < 3) {\n\t\t\t\tconsole.error(\"Not enough template IDs available:\", allTmplIds);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t// Ensure requiredTmplId is included, select 2 more randomly\n\t\t\tconst otherTmplIds = allTmplIds.filter(id => id !== requiredTmplId);\n\t\t\tconst shuffled = otherTmplIds.sort(() => 0.5 - Math.random());\n\t\t\tconst selectedTmplIds = [requiredTmplId, ...shuffled.slice(0, 2)];\n\t\t\tconsole.log(\"Selected template IDs:\", selectedTmplIds);\n\t\t\tconst templateData = selectedTmplIds.map((id, index) => ({\n\t\t\t\ttemplateId: id,\n\t\t\t\ttemplateCategoryId: index === 0 ? 10 : 5\n\t\t\t}));\n\t\t\tuni.requestSubscribeMessage({\n\t\t\t\ttmplIds: selectedTmplIds,\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('requestSubscribeMessage result:', res);\n\t\t\t\t\tthis.templateCategoryIds = [];\n\t\t\t\t\tlet count = 0;\n\t\t\t\t\tselectedTmplIds.forEach((templId, index) => {\n\t\t\t\t\t\tconsole.log(`Template ${templId} status: ${res[templId]}`);\n\t\t\t\t\t\tif (res[templId] === 'accept') {\n\t\t\t\t\t\t\tconst templateCategoryId = templateData[index].templateCategoryId;\n\t\t\t\t\t\t\tif (templateCategoryId === 10) {\n\t\t\t\t\t\t\t\tfor (let i = 0; i < 15; i++) {\n\t\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// Do not push if templateCategoryId is 5\n\t\t\t\t\t\t\t// else {\n\t\t\t\t\t\t\t// \tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t// }\n\t\t\t\t\t\t\tconsole.log('Accepted message push for template:', templId);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tconsole.log('Updated templateCategoryIds:', this.templateCategoryIds);\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('requestSubscribeMessage failed:', err);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\tloadMore() {\n\t\t\tif (this.status === 'nomore' || this.isLoading) return;\n\t\t\tthis.isLoading = true;\n\t\t\tthis.status = 'loading';\n\n\t\t\tconst nextPage = this.page + 1;\n\t\t\tthis.fetchOrders(nextPage, false);\n\t\t},\n\n\t\trefreshList() {\n\t\t\tthis.page = 0;\n\t\t\tthis.orderList = [];\n\t\t\tthis.status = 'loadmore';\n\t\t\tthis.getList();\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t}, 1000);\n\t\t},\n\n\t\tfetchOrders(pageNum, replaceList = true) {\n\t\t\treturn this.$api.shifu.master_Order({\n\t\t\t\tcoachId: this.shifuId,\n\t\t\t\tpayType: this.currentIndex === 8 ? 7 : this.currentIndex,\n\t\t\t\tpageNum: pageNum,\n\t\t\t\tpageSize: this.limit\n\t\t\t}).then(res => {\n\t\t\t\tif (res.code === '-1' || !res.data || !res.data.list) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: res.msg || '没有更多数据'\n\t\t\t\t\t});\n\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t} else {\n\t\t\t\t\tconst list = Array.isArray(res.data.list) ? res.data.list : [];\n\t\t\t\t\tconst normalizedList = list\n\t\t\t\t\t\t.filter(item => this.currentIndex !== 8 || item.isAftermarket === 1)\n\t\t\t\t\t\t.map(item => ({\n\t\t\t\t\t\t\t...item,\n\t\t\t\t\t\t\tpayType: parseInt(item.payType),\n\t\t\t\t\t\t\t// 确保 orderDiffPriceList 存在\n\t\t\t\t\t\t\torderDiffPriceList: Array.isArray(item.orderDiffPriceList) ? item.orderDiffPriceList : []\n\t\t\t\t\t\t}));\n\n\t\t\t\t\tif (replaceList) {\n\t\t\t\t\t\tthis.orderList = normalizedList;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.orderList = [...this.orderList, ...normalizedList];\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.page = pageNum;\n\t\t\t\t\tthis.status = list.length < this.limit ? 'nomore' : 'loadmore';\n\t\t\t\t}\n\t\t\t\tthis.isLoading = false;\n\t\t\t\treturn res;\n\t\t\t}).catch(err => {\n\t\t\t\tthis.status = 'nomore';\n\t\t\t\tthis.isLoading = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tconsole.error('Error loading data:', err);\n\t\t\t\treturn Promise.reject(err);\n\t\t\t});\n\t\t},\n\n\t\tgoDetail(item) {\n\t\t\tuni.setStorageSync('orderdetails', item);\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/shifu/master_order_my?id=${item.id}`\n\t\t\t});\n\t\t},\n\n\t\tgoUrl(e) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: e\n\t\t\t});\n\t\t},\n\n\t\tshowConfirmModal(item, action) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: action === 'queren' ? '确认到达' : '开始服务',\n\t\t\t\tcontent: '请确认操作：' + (action === 'queren' ? '确认到达' : '开始服务'),\n\t\t\t\tconfirmText: '确定',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tif (action === 'queren') {\n\t\t\t\t\t\t\tthis.queren(item);\n\t\t\t\t\t\t} else if (action === 'startFu') {\n\t\t\t\t\t\t\tthis.startFu(item);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\tasync startFu(item) {\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.shifu.shifuqueren({\n\t\t\t\t\tid: item.id,\n\t\t\t\t\tpayType: 6\n\t\t\t\t});\n\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '操作成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.refreshList();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg || '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tconsole.error('Error in startFu:', err);\n\t\t\t}\n\t\t},\n\n\t\tasync queren(item) {\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.shifu.shifuqueren({\n\t\t\t\t\tid: item.id,\n\t\t\t\t\tpayType: 5\n\t\t\t\t});\n\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '操作成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.refreshList();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg || '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tconsole.error('Error in queren:', err);\n\t\t\t}\n\t\t},\n\n\t\tupdateHigh(options) {\n\t\t\tconst shiInfo = uni.getStorageSync('shiInfo');\n\t\t\tif (!shiInfo) {\n\t\t\t\tconsole.log('No shiInfo, skipping updateHighlight');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tlet shiInfoid;\n\t\t\ttry {\n\t\t\t\tshiInfoid = JSON.parse(shiInfo);\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('Error parsing shiInfo:', e);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.$api.service.updataHighlight({\n\t\t\t\tuserId: shiInfoid.id,\n\t\t\t\trole: 2,\n\t\t\t\tpayType: options.tab\n\t\t\t}).then(res => {\n\t\t\t\tconsole.log(res);\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('Error updating highlight:', err);\n\t\t\t});\n\t\t},\n\n\t\tgetList() {\n\t\t\tif (this.isLoading) return;\n\t\t\tthis.isLoading = true;\n\t\t\tthis.status = 'loading';\n\t\t\tthis.fetchOrders(1, true);\n\t\t},\n\n\t\thandleHeader(item) {\n\t\t\tif (this.currentIndex === item.value) return;\n\t\t\tthis.currentIndex = item.value;\n\t\t\tthis.page = 0;\n\t\t\tthis.orderList = [];\n\t\t\tthis.status = 'loadmore';\n\t\t\tthis.getList();\n\t\t\tthis.updateHigh({\n\t\t\t\ttab: item.value\n\t\t\t});\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tlet shiInfo = uni.getStorageSync('shiInfo') || '{}';\n\t\ttry {\n\t\t\tthis.shifuId = JSON.parse(shiInfo).id;\n\t\t} catch (e) {\n\t\t\tconsole.error('Error parsing shiInfo:', e);\n\t\t\tthis.shifuId = '';\n\t\t}\n\n\t\tif (options.tab) {\n\t\t\tthis.currentIndex = parseInt(options.tab);\n\t\t}\n\t\tthis.updateHigh(options);\n\t\tthis.getList();\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tbackground-color: #F8F8F8;\n\theight: 100vh;\n\toverflow: auto;\n\tpadding-top: 100rpx;\n\n\t.header {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: 100;\n\t\twidth: 750rpx;\n\t\theight: 100rpx;\n\t\tbackground: #FFFFFF;\n\t\tdisplay: flex;\n\t\tjustify-content: space-around;\n\t\talign-items: center;\n\n\t\t.header_item {\n\t\t\tmax-width: 85rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #999999;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\tflex-wrap: wrap;\n\n\t\t\t.blue {\n\t\t\t\tmargin-top: 8rpx;\n\t\t\t\twidth: 38rpx;\n\t\t\t\theight: 6rpx;\n\t\t\t\tbackground: #2E80FE;\n\t\t\t\tborder-radius: 4rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.main {\n\t\tpadding: 20rpx 30rpx;\n\t\tmin-height: calc(100vh - 100rpx);\n\n\t\t.main_item {\n\t\t\twidth: 690rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder-radius: 24rpx;\n\t\t\tpadding: 28rpx 36rpx;\n\t\t\tmargin-bottom: 20rpx;\n\t\t\tbox-sizing: border-box;\n\n\t\t\t.head {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #999999;\n\n\t\t\t\t.no {\n\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.order-type-info {\n\t\t\t\tmargin-top: 15rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\n\t\t\t\t.order-type-row {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t.order-type-label {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: #666666;\n\t\t\t\t\t\tmargin-right: 8rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.order-type-value {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.urgent-indicator {\n\t\t\t\t\t.urgent-text {\n\t\t\t\t\t\tbackground: #FF6B35;\n\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\tpadding: 4rpx 12rpx;\n\t\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.mid {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\n\t\t\t\t.lef {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\timage {\n\t\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\t\theight: 120rpx;\n\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\ttext {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\tmargin-left: 30rpx;\n\t\t\t\t\t\tmax-width: 350rpx;\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.righ {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t\ttext-align: right;\n\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.bot {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #999999;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: flex-end;\n\t\t\t\talign-items: center;\n\t\t\t\tgap: 20rpx;\n\n\t\t\t\t.qzf {\n\t\t\t\t\twidth: 148rpx;\n\t\t\t\t\theight: 48rpx;\n\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\tborder-radius: 50rpx;\n\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tline-height: 48rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 子订单样式\n\t\t\t.sub_orders {\n\t\t\t\tmargin-top: 30rpx;\n\t\t\t\tpadding-top: 20rpx;\n\t\t\t\tborder-top: 1px solid #f0f0f0;\n\n\t\t\t\t.sub_title {\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t}\n\n\t\t\t\t.sub_scroll_container {\n\t\t\t\t\tmax-height: 450rpx;\n\t\t\t\t\toverflow-y: auto;\n\t\t\t\t}\n\n\t\t\t\t.sub_item {\n\t\t\t\t\tbackground: #f8f9fa;\n\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\tpadding: 20rpx;\n\t\t\t\t\tmargin-bottom: 15rpx;\n\n\t\t\t\t\t.sub_head {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tmargin-bottom: 15rpx;\n\n\t\t\t\t\t\t.sub_no {\n\t\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\t\tmax-width: 400rpx;\n\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.sub_status {\n\t\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sub_content {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\talign-items: flex-start;\n\n\t\t\t\t\t\t.sub_info_grid {\n\t\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\tmargin-right: 20rpx;\n\n\t\t\t\t\t\t\t.sub_info_row {\n\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t\t\t\t\tgap: 20rpx;\n\n\t\t\t\t\t\t\t\t&:last-child {\n\t\t\t\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.sub_info_item {\n\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\t\t\tmin-width: 0;\n\n\t\t\t\t\t\t\t\t\t&.sub_reason_item {\n\t\t\t\t\t\t\t\t\t\tflex: 2;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t.sub_label {\n\t\t\t\t\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\t\t\t\t\tmargin-right: 8rpx;\n\t\t\t\t\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t.sub_value {\n\t\t\t\t\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\t\t\t\tmin-width: 0;\n\n\t\t\t\t\t\t\t\t\t\t&.sub_amount_value {\n\t\t\t\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\t\t\t\tcolor: #ff6b35;\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t&.sub_warranty_value {\n\t\t\t\t\t\t\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t&.sub_reason_value {\n\t\t\t\t\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t&.sub_time_value {\n\t\t\t\t\t\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.sub_actions {\n\t\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t\t\talign-self: flex-start;\n\n\t\t\t\t\t\t\t.sub_qzf {\n\t\t\t\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\t\t\t\theight: 40rpx;\n\t\t\t\t\t\t\t\tbackground: #ff6b6b;\n\t\t\t\t\t\t\t\tborder-radius: 40rpx;\n\t\t\t\t\t\t\t\tfont-size: 18rpx;\n\t\t\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\t\t\tline-height: 40rpx;\n\t\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n.slot-content {\n\tpadding: 30rpx;\n}\n\n.upload-container {\n\tmargin-top: 16rpx;\n\tpadding: 20rpx;\n\tborder: 1rpx dashed #ccc;\n\tborder-radius: 8rpx;\n\tbackground: #fafafa;\n}\n\n.parts-img {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tmargin-right: 12rpx;\n\tmargin-bottom: 12rpx;\n\tborder-radius: 8rpx;\n}\n\n/* 简洁的差价申请弹窗样式 */\n.diff-apply-modal {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbackground: rgba(0, 0, 0, 0.5);\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tz-index: 9999;\n}\n\n.modal-content {\n\twidth: 90%;\n\tmax-width: 600rpx;\n\tbackground: #fff;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n\toverflow: hidden;\n}\n\n.modal-header {\n\tposition: relative;\n\tpadding: 40rpx 50rpx 20rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.modal-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\ttext-align: center;\n}\n\n.close-btn {\n\tposition: absolute;\n\ttop: 20rpx;\n\tright: 20rpx;\n\twidth: 48rpx;\n\theight: 48rpx;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tborder-radius: 50%;\n\tbackground: #f5f5f5;\n\ttransition: background 0.2s;\n}\n\n.close-btn:active {\n\tbackground: #e0e0e0;\n}\n\n.close-icon {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1;\n}\n\n.modal-body {\n\tpadding: 40rpx;\n}\n\n.modal-footer {\n\tpadding: 20rpx 40rpx 40rpx;\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n\n.btn-cancel,\n.btn-confirm {\n\tflex: 1;\n\theight: 80rpx;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tborder-radius: 8rpx;\n\tfont-size: 28rpx;\n\ttransition: all 0.2s;\n}\n\n.btn-cancel {\n\tbackground: #f8f8f8;\n\tcolor: #666;\n\tborder: 1rpx solid #ddd;\n}\n\n.btn-cancel:active {\n\tbackground: #e8e8e8;\n}\n\n.btn-confirm {\n\tbackground: #007aff;\n\tcolor: #fff;\n\tborder: 1rpx solid #007aff;\n}\n\n.btn-confirm:active {\n\tbackground: #0056cc;\n}\n\n/* 简洁表单样式 */\n.modal-body /deep/ .u-form-item {\n\tmargin-bottom: 24rpx;\n\tpadding-bottom: 20rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.modal-body /deep/ .u-form-item:last-child {\n\tborder-bottom: none;\n\tmargin-bottom: 0;\n}\n\n.modal-body /deep/ .u-form-item__label {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-bottom: 12rpx;\n\tfont-weight: 500;\n}\n\n.modal-body /deep/ .u--input__content {\n\tbackground: #f8f8f8;\n\tborder: 1rpx solid #e0e0e0;\n\tborder-radius: 8rpx;\n\tpadding: 16rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.modal-body /deep/ .u--textarea__content {\n\tbackground: #f8f8f8;\n\tborder: 1rpx solid #e0e0e0;\n\tborder-radius: 8rpx;\n\tpadding: 16rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmin-height: 120rpx;\n}\n\n/* 原因类型显示样式 */\n.reason-type-display {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 16rpx;\n\tbackground: #f8f8f8;\n\tborder: 1rpx solid #e0e0e0;\n\tborder-radius: 8rpx;\n}\n\n.reason-type-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.reason-type-badge {\n\tbackground: #007aff;\n\tcolor: #fff;\n\tfont-size: 20rpx;\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 12rpx;\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=style&index=0&id=b530dfe4&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=style&index=0&id=b530dfe4&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755660949636\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}