{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/mine.vue?9252", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/mine.vue?e768", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/mine.vue?18b9", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/mine.vue?25f3", "uni-app:///shifu/mine.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/mine.vue?8d3c", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/mine.vue?60ec"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "args", "clearTimeout", "timeout", "components", "tabbar", "data", "isLoading", "inviteCode", "tmplIds", "code", "labelName", "loginPopupVisible", "agreedToTerms", "shifusta<PERSON>", "orderList", "icon", "text", "url", "count", "orderList3", "toolList2", "iconColor", "computed", "userInfo", "token", "er<PERSON><PERSON>", "regeocode", "isLoggedIn", "displayUserInfo", "phone", "avatarUrl", "nick<PERSON><PERSON>", "userId", "pid", "statusText", "statusBadgeClass", "watch", "handler", "deep", "immediate", "onLoad", "console", "uni", "provider", "success", "fail", "onShow", "onPullDownRefresh", "Promise", "methods", "getmyG<PERSON>", "getmylogin", "getNowPosition", "type", "isHighAccuracy", "accuracy", "requestError", "amapResponse", "province", "city", "adcode", "position", "lat", "lng", "city_id", "getshifuinfo", "mobile", "address", "cityId", "labelId", "<PERSON><PERSON><PERSON>", "id", "status", "messagePush", "key", "val", "debounceGetHighlight", "getHighlight", "role", "item", "index", "handleContact", "showLoginPopup", "hideLoginPopup", "toggleAgreement", "navigateToAgreement", "initUserData", "navigateTo", "saveUserInfoToStorage", "handleInvalidSession", "onGetPhoneNumber", "mask", "title", "e", "encryptedData", "iv", "loginWithWeixin", "response", "userInfoRes", "initialUserInfo", "shifuStatusRes", "userinster", "registerRes", "masterRess", "masterRes", "shufuId", "modalShownKey", "hasShownModal", "showToast", "duration", "fetchShifuInfo", "shiInfoResponses", "shiInfoResponse", "shifuId", "defaultUserInfo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showCertificationPopup", "content", "confirmText", "cancelText", "cancelable", "handleNavigate", "handleCallKf", "callkf"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAm1B,CAAgB,m2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;AC6Jv2B;AAGA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AACA;EACA;EACA;IAAA;MAAAC;IAAA;IACA;IACAC;IACAC;MAAA;IAAA;EACA;AACA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC,UACA,gDACA,+CACA,8CACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,EACA;MACAC;QACAJ;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACAG;QACAL;QACAC;QACAC;QACAI;MACA,GACA;QACAN;QACAC;QACAC;QACAI;MACA;IAEA;EACA;EACAC,0CACA;IACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;MACA;IACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;UACA;MAAA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;IACA;EAAA,EACA;EACAC;IACA;IACAb;MACAc;QAAA;QACA;QACA;UACA;UACA;UACA;YACA;UACA;QACA;MACA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;MACAC;MACA;MACAC;IACA;IACA;MACAD;MACA;MACAC;IACA;MACA;MACA;QACAD;QACA;QACA;QACAC;MACA;IACA;IACAA;MACAC;MACAC;QACA;UACA;UACAH;QACA;MACA;MACAI;QACAJ;MACA;IACA;IACA;IACA;MACA;IACA;IACA;IACA;IACA;EACA;EACAK;IAAA;IACA;MACA;IACA;MACA;IACA;IACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;MACAC,aACA,qBACA,sBACA;QACAN;MACA;QACAD;QACAC;MACA;IACA;MACA;MACAA;MACA;IACA;EACA;EACAO;IACAC;MAAA;MACA;QACAT;QACA;MACA;IACA;IACAU;MAAA;MACAT;QACAC;QACAC;UACA;YACA;YACAH;UACA;QACA;MACA;IACA;IACAW;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAV;oBACAW;oBACAC;oBACAC;oBACAX;sBAAA;wBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCACAH;gCACAC;gCACAA;gCAAA;gCAAA;gCAAA,OAEAA;kCACAzB;gCACA;8BAAA;gCAAA;gCAAA;gCAFAuC;gCAAAC;gCAGA;kCAAA,wBAKAA,8CAHAC,2CACAC,mCACAC;kCAEAC;kCACA;oCACAnC;oCACAoC;oCACAC;kCACA;kCACArB;oCACAsB;oCACAH;kCACA;kCACA;oCACAnC;oCACAoC;oCACAC;kCACA;kCACAtB;oCAAAuB;oCAAAH;kCAAA;gCACA;gCAAA;gCAAA;8BAAA;gCAAA;gCAAA;gCAEApB;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CAEA;sBAAA;wBAAA;sBAAA;sBAAA;oBAAA;oBACAI;sBACAJ;oBACA;kBACA;gBACA;kBACAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAwB;MAAA;MACA;MACA;QACAxB;QACA;MACA;MACA;QACAT;MACA;QACAS;QACA;QACA;UAAA;UACA;YACAT;YACAkC;YACAC;YACAC;YACAC;YACAN;YACAD;UACA;UACArB;UACA;YACA;cACAA;cACA;YACA;cACA;YACA;UACA;YACA;cACA;YACA;YACA;YACA;cACAZ;cACAC;cACAC;cACAC;cACAC;YACA;YACAS;cACAwB;cACApC;cACAwC;cACAC;cACAtC;cACAuC;cACAC;YACA;YACA;cACAC;cACAC;YACA;YACA;YACA;cACA;YACA;UACA;QACA;UACA;YACA;cACA;YACA;YACA;YACA;cACA9C;cACAC;cACAC;cACAC;cACAC;YACA;YACAS;cACAwB;cACApC;cACAwC;cACAC;cACAtC;cACAuC;cACAC;YACA;YACA;cACAC;cACAC;YACA;YACA;YACA;cACA;YACA;UACA;QACA;MACA;QACAlC;QACA;QACA;UACAZ;UACAC;UACAC;UACAC;UACAC;QACA;QACAS;UACAwB;UACApC;UACAwC;UACAC;UACAtC;UACAuC;UACAC;QACA;QACA;UACAC;UACAC;QACA;QACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACApC;QACA;MACA;MACA;MACA;QACAT;QACA8C;MACA;QACArC;QACA;UAAA,uCACAsC;YACA7D,kEACA8D,6DACAA,uDACAA,qDACAA,yDACAA;UAAA;QAAA,CACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACAxC;MACAA;IACA;EAAA,GACA;IACAyC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACApE;MACA;QACAA;MACA;MACAyB;QACAzB;MACA;IACA;IACAqE;MAAA;MACA;QACA;UACAzD;UACAC;UACAC;UACAC;UACAC;QACA;QACA;UACA;YACAyC;YACAC;UACA;UACA;UACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAY;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA,CACA;MACA;QAAA;MAAA;QACA;MACA;MACA7C;QACAzB;MACA;IACA;IACAuE;MACA9C;MACAA;MACAA;MACAA;MACAA;IACA;IACA+C;MAAA;MACA;QACA/C;MACA;MACAA;MACA;QACAgC;QACAC;MACA;MACA;QACAD;QACAC;MACA;MACA;MACA;QAAA,uCACAI;UACA7D;QAAA;MAAA,CACA;MACA;QACA;MACA;IACA;IACAwE;MAAA;MAOA;QACA;MACA;MACA;MACA;MACAhD;QACAiD;QACAC;MACA;MACA,gBAGAC;QAFAC;QACAC;MAEArD;QACAE;UACA;YACAnC;YACAqF;YACAC;YACA9D;UACA;QACA;QACAY;UACAH;YACAC;YACAC;cACA;gBACA;gBACAH;gBACA;kBACAhC;kBACAqF;kBACAC;kBACA9D;gBACA;cACA;gBACA;gBACAS;gBACA;cACA;YACA;YACAG;cACA;cACAH;cACA;YACA;UACA;QACA;MACA;IACA;IACAsD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAvF;kBACAqF;kBACAC;kBACA9D;gBACA;cAAA;gBALAgE;gBAMAxD;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAEAC;gBACA;kBACAgC;kBACAC;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAuB;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAEAzD;gBACA0D;kBACAtE;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBACA;kBACAyC;kBACAC;gBACA;gBACA;gBAAA;gBAAA,OACA;kBACA3C;gBACA;cAAA;gBAFAoE;gBAGA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAC;kBACArE;kBACAkC;kBACAC;kBACAC;kBACAC;kBACAN;kBACAD;gBACA;gBACArB;gBAAA;gBAAA,OACA;cAAA;gBAAA6D;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAEA7D;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA8D;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAEAC;gBACAjF;kBACAM;kBACAC;kBACAC;kBACAC;kBACAyE;kBACAxE;gBACA;gBACAS;kBACAwB;kBACApC;kBACAwC;kBACAmC;kBACAzE;kBAEAC;kBACAuC;kBACAC;gBACA;gBACA;kBACAC;kBACAC;gBACA;gBACA;gBACA+B;gBACAC;gBACA;kBACA;kBACAjE;gBACA;gBACA;gBACA;gBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAkE;MAAA;MACAlE;QACAkD;QACA7E;QACA8F;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACAC;gBACAvE;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAEA;gBACAlB;kBACAM;kBACAC;kBACAC;kBACAkF;kBACAjF;kBACAC;gBACA;gBACAS;kBACAwB;kBACApC;kBACAwC;kBACAtC;kBACAiF;kBACAhF;kBACAuC;kBACAC;gBACA;gBACA;kBACAC;kBACAC;gBACA;gBACA+B;gBACAC;gBACA;kBACA;kBACAjE;gBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;gBACA;gBACAyE;kBACArF;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBACAS;kBACAwB;kBACApC;kBACAwC;kBACAtC;kBACAiF;kBACAhF;kBACAuC;kBACAC;gBACA;gBACA;kBACAC;kBACAC;gBACA;gBACA+B;gBACAC;gBACA;kBACA;kBACAjE;gBACA;gBACA;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAyE;MACA;MACA;MACA1E;IACA;IACA2E;MACA3E;MACA;QACAC;UACAkD;UACAyB;UACAC;UACAC;UACAC;UACA5E;YACA;cACA;cACAF;gBACAzB;gBACA4B;kBACAJ;kBACAC;oBACAkD;oBACA7E;kBACA;gBACA;cACA;YACA;UACA;UACA8B;YACAJ;UACA;QACA;MACA;IACA;IACAgF;MACA;MAEA;QACA;QACA;MACA;MAEA;QACA/E;UACAkD;UACA7E;QACA;QACA;MACA;QACA2B;UACAkD;UACA7E;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA2G;MACA;QACAhF;UACAkD;UACA7E;QACA;QACA;MACA;QACA2B;UACAkD;UACA7E;QACA;MACA;QACA;MACA;IACA;IACA4G;MACAjF;QACAkD;QACA7E;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;AC3hCA;AAAA;AAAA;AAAA;AAAkkD,CAAgB,shDAAG,EAAC,C;;;;;;;;;;;ACAtlD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/mine.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/mine.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mine.vue?vue&type=template&id=405232ed&\"\nvar renderjs\nimport script from \"./mine.vue?vue&type=script&lang=js&\"\nexport * from \"./mine.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mine.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/mine.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=template&id=405232ed&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=script&lang=js&\"", "\n<template>\n\t<view class=\"pages-mine\">\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header-content\">\n\t\t\t\t<view class=\"avatar_view\">\n\t\t\t\t\t<image mode=\"aspectFill\" class=\"avatar\" :src=\"displayUserInfo.avatarUrl || '/static/mine/default_user.png'\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t<view v-if=\"!isLoggedIn\">\n\t\t\t\t\t\t<button @click=\"showLoginPopup\" :disabled=\"isLoading\" :class=\"{ 'loading': isLoading }\">\n\t\t\t\t\t\t\t{{ isLoading ? '登录中...' : '用户登录' }}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else class=\"user-info-logged\">\n\t\t\t\t\t\t<view class=\"nickname\">\n\t\t\t\t\t\t\t{{ displayUserInfo.nickName }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"phone-number\" v-if=\"displayUserInfo.phone\">\n\t\t\t\t\t\t\t{{ displayUserInfo.phone }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"status-badge\" :class=\"statusBadgeClass\">\n\t\t\t\t\t\t\t{{ statusText}}\n\t\t\t\t\t\t\t{{ labelName }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view @click=\"navigateTo('../user/userProfile')\" class=\"settings\">\n\t\t\t\t\t<i class=\"iconfont icon-xitong text-bold\"></i>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"mine-menu-list box-shadow fill-base box1\">\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-md b-1px-b\">\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">我的订单</view>\n\t\t\t</view>\n\t\t\t<view class=\"flex-warp pt-lg pb-lg\">\n\t\t\t\t<view class=\"order-item\" v-for=\"(item, index) in orderList\" :key=\"index\" @tap=\"navigateTo(item.url)\">\n\t\t\t\t\t<view class=\"icon-container\">\n\t\t\t\t\t\t<u-icon :name=\"item.icon\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t\t<view v-if=\"item.count > 0\" class=\"number-circle\">{{ item.count }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"mt-sm\">{{ item.text }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"mine-menu-list box-shadow fill-base\">\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-md b-1px-b\">\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">常用功能</view>\n\t\t\t</view>\n\t\t\t<view class=\"flex-warp pt-lg pb-lg\">\n\t\t\t\t<view class=\"order-item\" v-for=\"(item, index) in orderList3\" :key=\"index\"\n\t\t\t\t\t@tap=\"handleNavigate(item.url)\">\n\t\t\t\t\t<u-icon :name=\"item.icon\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t<view class=\"mt-sm\">{{ item.text }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"mine-menu-list box-shadow fill-base\">\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-md b-1px-b\">\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">其他功能</view>\n\t\t\t</view>\n\t\t\t<view class=\"flex-warp pt-lg pb-lg\">\n\t\t\t\t<view class=\"order-item\" @tap=\"handleNavigate('/shifu/skills')\">\n\t\t\t\t\t<u-icon name=\"plus-square-fill\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t<view class=\"mt-sm\">技能标签</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-item\" @tap=\"handleNavigate('/shifu/Professiona')\">\n\t\t\t\t\t<u-icon name=\"order\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t<view class=\"mt-sm\">技能证书</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-item\" @tap=\"handleNavigate('/user/promotion')\">\n\t\t\t\t\t<u-icon name=\"red-packet-fill\" color=\"#E41F19\" size=\"28\"></u-icon>\n\t\t\t\t\t<view style=\"color: #E41F19;\" class=\"mt-sm\">邀请有礼</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"spacer\"></view>\n\n\t\t<view class=\"mine-tool-grid fill-base\">\n\t\t\t<view class=\"grid-container\">\n\t\t\t\t<view class=\"grid-item\" v-for=\"(item, index) in toolList2\" :key=\"index\" @tap=\"handleNavigate(item.url)\">\n\t\t\t\t\t<view class=\"grid-icon-container\">\n\t\t\t\t\t\t<u-icon :name=\"item.icon\" :color=\"item.iconColor\" size=\"28\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"grid-text\">{{ item.text }}</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"grid-item\" @tap=\"navigateTo('../pages/service')\">\n\t\t\t\t\t<view class=\"grid-icon-container switch-identity\">\n\t\t\t\t\t\t<u-icon name=\"man-add\" color=\"#E41F19\" size=\"28\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view style=\"color: #E41F19;\" class=\"grid-text\">切换用户版</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"grid-item\">\n\t\t\t\t\t<button class=\"contact-btn-wrapper\" open-type=\"contact\" bindcontact=\"handleContact\"\n\t\t\t\t\t\tsession-from=\"sessionFrom\">\n\t\t\t\t\t\t<view class=\"grid-icon-container switch-identity\">\n\t\t\t\t\t\t\t<u-icon name=\"server-man\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"grid-text\">客服</view>\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"floating-contact\">\n\t\t\t<view class=\"contact-container\">\n\t\t\t\t<u-icon name=\"server-man\" color=\"#576b95\" size=\"24\"></u-icon>\n\t\t\t\t<button class=\"contact-btn\" open-type=\"contact\" bindcontact=\"handleContact\" session-from=\"sessionFrom\">\n\t\t\t\t\t客服\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view v-if=\"loginPopupVisible\" class=\"login-popup-overlay\" @tap=\"hideLoginPopup\">\n\t\t\t<view class=\"login-popup\" @tap.stop>\n\t\t\t\t<view class=\"close-btn\" @tap=\"hideLoginPopup\">\n\t\t\t\t\t<i class=\"iconfont icon-close\"></i>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"popup-content\">\n\t\t\t\t\t<view class=\"welcome-title\">欢迎登录今师傅</view>\n\t\t\t\t\t<view class=\"welcome-subtitle\">登录后即可享受完整服务</view>\n\n\t\t\t\t\t<view class=\"agreement-section\">\n\t\t\t\t\t\t<view class=\"checkbox-container\" @tap=\"toggleAgreement\">\n\t\t\t\t\t\t\t<view class=\"checkbox\" :class=\"{ 'checked': agreedToTerms }\">\n\t\t\t\t\t\t\t\t<i v-if=\"agreedToTerms\" class=\"iconfont icon-check\">✓</i>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"agreement-text\">\n\t\t\t\t\t\t\t\t我已阅读并同意 <text class=\"link\" @tap.stop=\"navigateToAgreement('service')\">《今师傅服务协议》</text>\n\t\t\t\t\t\t\t\t<text class=\"link\" @tap.stop=\"navigateToAgreement('privacy')\">《隐私政策》</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<button class=\"phone-login-btn\" :class=\"{ 'disabled': !agreedToTerms || isLoading }\"\n\t\t\t\t\t\t:disabled=\"!agreedToTerms || isLoading\" open-type=\"getPhoneNumber\"\n\t\t\t\t\t\t@getphonenumber=\"onGetPhoneNumber\">\n\t\t\t\t\t\t{{ isLoading ? '登录中...' : '手机号快捷登录' }}\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<tabbar cur=\"1\"></tabbar>\n\t</view>\n</template>\n\n<script>\n\timport tabbar from \"@/components/tabbarsf.vue\";\n\timport {\n\t\tmapState,\n\t\tmapMutations\n\t} from \"vuex\";\n\n\t// Utility function for debouncing\n\tconst debounce = (func, wait) => {\n\t\tlet timeout;\n\t\treturn function(...args) {\n\t\t\tconst context = this;\n\t\t\tclearTimeout(timeout);\n\t\t\ttimeout = setTimeout(() => func.apply(context, args), wait);\n\t\t};\n\t};\n\n\texport default {\n\t\tcomponents: {\n\t\t\ttabbar\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisLoading: false,\n\t\t\t\tinviteCode: '',\n\t\t\t\ttmplIds: [\n\t\t\t\t\t' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo',\n\t\t\t\t\t'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs',\n\t\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t\t],\n\t\t\t\tcode: '',\n\t\t\t\tlabelName:'',\n\t\t\t\tloginPopupVisible: false,\n\t\t\t\tagreedToTerms: false,\n\t\t\t\tshifustatus: -1, // Initialize with a default numeric value\n\t\t\t\torderList: [{\n\t\t\t\t\t\ticon: 'order',\n\t\t\t\t\t\ttext: '全部',\n\t\t\t\t\t\turl: '/shifu/master_my_order?tab=0',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'bell',\n\t\t\t\t\t\ttext: '待上门',\n\t\t\t\t\t\turl: '/shifu/master_my_order?tab=3',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'hourglass-half-fill',\n\t\t\t\t\t\ttext: '待服务',\n\t\t\t\t\t\turl: '/shifu/master_my_order?tab=5',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'clock',\n\t\t\t\t\t\ttext: '服务中',\n\t\t\t\t\t\turl: '/shifu/master_my_order?tab=6',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'thumb-up',\n\t\t\t\t\t\ttext: '已完成',\n\t\t\t\t\t\turl: '/shifu/master_my_order?tab=7',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'chat-fill',\n\t\t\t\t\t\ttext: '售后',\n\t\t\t\t\t\turl: '/shifu/master_my_order?tab=8',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\torderList3: [{\n\t\t\t\t\t\ticon: 'red-packet',\n\t\t\t\t\t\ttext: '服务收入',\n\t\t\t\t\t\turl: '/shifu/income'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'file-text-fill',\n\t\t\t\t\t\ttext: '报价列表',\n\t\t\t\t\t\turl: '/shifu/master_bao_list'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'rmb-circle',\n\t\t\t\t\t\ttext: '保证金',\n\t\t\t\t\t\turl: '/shifu/Margin'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'rmb-circle',\n\t\t\t\t\t\ttext: '师傅等级',\n\t\t\t\t\t\turl: '/shifu/shifuGrade'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttoolList2: [{\n\t\t\t\t\t\ticon: 'plus-people-fill',\n\t\t\t\t\t\ttext: '师傅入驻',\n\t\t\t\t\t\turl: '/shifu/Settle',\n\t\t\t\t\t\ticonColor: '#448cfb'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'edit-pen',\n\t\t\t\t\t\ttext: '编辑师傅资料',\n\t\t\t\t\t\turl: '/shifu/master_Info',\n\t\t\t\t\t\ticonColor: '#448cfb'\n\t\t\t\t\t},\n\t\t\t\t]\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\t...mapState({\n\t\t\t\t// Changed from storeUserInfo to userInfo based on user's request and Vuex module\n\t\t\t\tuserInfo: state => state.user.userInfo || {},\n\t\t\t\ttoken: state => state.user.autograph || '',\n\t\t\t\terweima: state => state.user.erweima || '',\n\t\t\t\tregeocode: (state) => state.service.regeocode,\n\t\t\t}),\n\t\t\tisLoggedIn() {\n\t\t\t\t// Ensure userInfo and its properties are checked safely\n\t\t\t\treturn !!this.token && !!this.userInfo.phone;\n\t\t\t},\n\t\t\t// This computed property will combine Vuex userInfo with local storage `shiInfo`\n\t\t\t// for display purposes, giving `shiInfo` priority where it makes sense.\n\t\t\tdisplayUserInfo() {\n\t\t\t\tconst shiInfo = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};\n\t\t\t\t// Prioritize shiInfo for display, then Vuex userInfo, then uni.getStorageSync, then defaults\n\t\t\t\tconst avatarUrl = shiInfo.avatarUrl || this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png';\n\t\t\t\tconst nickName = shiInfo.coachName || this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户';\n\t\t\t\tconst phone = shiInfo.mobile || this.userInfo.phone || uni.getStorageSync('phone') || '';\n\t\t\t\tconst userId = shiInfo.id || this.userInfo.userId || uni.getStorageSync('userId') || '';\n\t\t\t\tconst pid = shiInfo.pid || this.userInfo.pid || uni.getStorageSync('pid') || '';\n\n\t\t\t\treturn {\n\t\t\t\t\tphone: this.isLoggedIn ? phone : '',\n\t\t\t\t\tavatarUrl: this.isLoggedIn ? avatarUrl : '/static/mine/default_user.png',\n\t\t\t\t\tnickName: this.isLoggedIn ? nickName : '微信用户',\n\t\t\t\t\tuserId: this.isLoggedIn ? userId : '',\n\t\t\t\t\tpid: this.isLoggedIn ? pid : ''\n\t\t\t\t};\n\t\t\t},\n\t\t\tstatusText() {\n\t\t\t\tswitch (this.shifustatus) {\n\t\t\t\t\tcase -1:\n\t\t\t\t\t\treturn '未入驻师傅';\n\t\t\t\t\tcase 1:\n\t\t\t\t\t\treturn '审核中';\n\t\t\t\t\tcase 2:\n\t\t\t\t\t\treturn '已认证';\n\t\t\t\t\tcase 4:\n\t\t\t\t\t\treturn '审核驳回';\n\t\t\t\t\tdefault:\n\t\t\t\t\t\t// Provide a fallback text if shifustatus is an unexpected number or still not set meaningfully\n\t\t\t\t\t\treturn '未知状态';\n\t\t\t\t}\n\t\t\t},\n\t\t\tstatusBadgeClass() {\n\t\t\t\treturn {\n\t\t\t\t\t'status-not-registered': this.shifustatus === -1,\n\t\t\t\t\t'status-pending': this.shifustatus === 1,\n\t\t\t\t\t'status-approved': this.shifustatus === 2,\n\t\t\t\t\t'status-rejected': this.shifustatus === 4\n\t\t\t\t};\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\t// Watch for changes in the Vuex userInfo and trigger updates\n\t\t\tuserInfo: {\n\t\t\t\thandler(newVal, oldVal) {\n\t\t\t\t\t// Only update if there's a meaningful change to avoid infinite loops\n\t\t\t\t\tif (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {\n\t\t\t\t\t\tthis.saveUserInfoToStorage(newVal);\n\t\t\t\t\t\t// Force update if necessary, though Vue's reactivity should handle most cases\n\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true, // Watch for nested property changes\n\t\t\t\timmediate: true // Run the handler immediately on component mount\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\tthis.getNowPosition();\n\t\t\t\tthis.getmyGrade();\n\t\t\tif (options.inviteCode) {\n\t\t\t\tconsole.log('Received inviteCode:', options.inviteCode);\n\t\t\t\tthis.inviteCode = options.inviteCode;\n\t\t\t\tuni.setStorageSync('receivedInviteCode', options.inviteCode);\n\t\t\t}\n\t\t\tif (this.erweima) {\n\t\t\t\tconsole.log('erweima from Vuex:', this.erweima);\n\t\t\t\tthis.inviteCode = this.erweima;\n\t\t\t\tuni.setStorageSync('receivedInviteCode', this.erweima);\n\t\t\t} else {\n\t\t\t\tconst erweima = uni.getStorageSync('erweima');\n\t\t\t\tif (erweima) {\n\t\t\t\t\tconsole.log('erweima from storage:', erweima);\n\t\t\t\t\tthis.$store.commit('setErweima', erweima);\n\t\t\t\t\tthis.inviteCode = erweima;\n\t\t\t\t\tuni.setStorageSync('receivedInviteCode', erweima);\n\t\t\t\t}\n\t\t\t}\n\t\t\tuni.login({\n\t\t\t\tprovider: 'weixin',\n\t\t\t\tsuccess: res => {\n\t\t\t\t\tif (res.code) {\n\t\t\t\t\t\tthis.code = res.code;\n\t\t\t\t\t\tconsole.log('Initial wx.login code:', this.code);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: err => {\n\t\t\t\t\tconsole.error('wx.login failed:', err);\n\t\t\t\t}\n\t\t\t});\n\t\t\tthis.initUserData();\n\t\t\tif (this.isLoggedIn) {\n\t\t\t\tthis.debounceGetHighlight();\n\t\t\t}\n\t\t\tthis.fetchShifuInfo();\n\t\t\t// No need to call getHighlight here again, as it's called after fetchShifuInfo in onShow\n\t\t\t// and also in fetchShifuInfo's success path if needed.\n\t\t},\n\t\tonShow() {\n\t\t\tif (this.isLoggedIn) {\n\t\t\t\tthis.debounceGetHighlight();\n\t\t\t} else {\n\t\t\t\tthis.handleInvalidSession();\n\t\t\t}\n\t\t\tthis.fetchShifuInfo();\n\t\t\tthis.getHighlight();\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.$forceUpdate();\n\t\t\t});\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tif (this.isLoggedIn) {\n\t\t\t\tPromise.all([\n\t\t\t\t\tthis.getHighlight(),\n\t\t\t\t\tthis.fetchShifuInfo()\n\t\t\t\t]).then(() => {\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('Pull-down refresh failed:', err);\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tthis.handleInvalidSession();\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\tthis.showToast('请先登录');\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tgetmyGrade(){\n\t\t\t\tthis.$api.shifu.getGrade().then(res=>{\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tthis.labelName=res.data.labelName\n\t\t\t\t})\n\t\t\t},\n\t\t\tgetmylogin() {\n\t\t\t\tuni.login({\n\t\t\t\t\tprovider: 'weixin',\n\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\tif (res.code) {\n\t\t\t\t\t\t\tthis.code = res.code;\n\t\t\t\t\t\t\tconsole.log('Initial wx.login code:', this.code);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tasync getNowPosition() {\n\t\t\t\ttry {\n\t\t\t\t\tuni.getLocation({\n\t\t\t\t\t\ttype: \"gcj02\",\n\t\t\t\t\t\tisHighAccuracy: true,\n\t\t\t\t\t\taccuracy: \"best\",\n\t\t\t\t\t\tsuccess: async (locationRes) => {\n\t\t\t\t\t\t\tconsole.log(\"Location success:\", locationRes);\n\t\t\t\t\t\t\tuni.setStorageSync(\"lat\", locationRes.latitude);\n\t\t\t\t\t\t\tuni.setStorageSync(\"lng\", locationRes.longitude);\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tconst [requestError, amapResponse] = await uni.request({\n\t\t\t\t\t\t\t\t\turl: `https://restapi.amap.com/v3/geocode/regeo?key=2036e9b214b103fcb49c00a23de129e3&location=${locationRes.longitude},${locationRes.latitude}`,\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tif (amapResponse && amapResponse.data && amapResponse.data.regeocode) {\n\t\t\t\t\t\t\t\t\tconst {\n\t\t\t\t\t\t\t\t\t\tprovince,\n\t\t\t\t\t\t\t\t\t\tcity,\n\t\t\t\t\t\t\t\t\t\tadcode\n\t\t\t\t\t\t\t\t\t} = amapResponse.data.regeocode.addressComponent;\n\t\t\t\t\t\t\t\t\tconst position = typeof city === \"string\" ? city : province;\n\t\t\t\t\t\t\t\t\tthis.setRegeocode({\n\t\t\t\t\t\t\t\t\t\tregeocode: amapResponse.data.regeocode,\n\t\t\t\t\t\t\t\t\t\tlat: locationRes.latitude,\n\t\t\t\t\t\t\t\t\t\tlng: locationRes.longitude\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\tuni.setStorageSync(\"city\", {\n\t\t\t\t\t\t\t\t\t\tcity_id: adcode,\n\t\t\t\t\t\t\t\t\t\tposition: position\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\tthis.$store.dispatch('setRegeocode', {\n\t\t\t\t\t\t\t\t\t\tregeocode: amapResponse.data.regeocode,\n\t\t\t\t\t\t\t\t\t\tlat: locationRes.latitude,\n\t\t\t\t\t\t\t\t\t\tlng: locationRes.longitude\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\tconsole.log(\"逆地理编码成功，城市信息:\", { city_id: adcode, position: position });\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} catch (regeoError) {\n\t\t\t\t\t\t\t\tconsole.error('Reverse geocoding failed:', regeoError);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('Get location failed:', err);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (outerError) {\n\t\t\t\t\tconsole.error('Get location outer error:', outerError);\n\t\t\t\t}\n\t\t\t},\n\t\t\tgetshifuinfo() {\n\t\t\t\tconst userId = this.userInfo.userId || uni.getStorageSync('userId');\n\t\t\t\tif (!userId) {\n\t\t\t\t\tconsole.log('No userId, skipping getshifuinfo');\n\t\t\t\t\treturn Promise.resolve();\n\t\t\t\t}\n\t\t\t\treturn this.$api.shifu.getshifstutas({\n\t\t\t\t\tuserId: userId\n\t\t\t\t}).then(res => {\n\t\t\t\t\tconsole.log('getshifstutas response:', res);\n\t\t\t\t\tthis.shifustatus = Number(res.data) !== undefined ? Number(res.data) : -1;\n\t\t\t\t\tif (res.data === -1) {\n\t\t\t\t\t\tconst userinster = {\n\t\t\t\t\t\t\tuserId: this.userInfo.userId || uni.getStorageSync('userId'),\n\t\t\t\t\t\t\tmobile: this.userInfo.phone || uni.getStorageSync('phone'),\n\t\t\t\t\t\t\taddress: this.regeocode?.regeocode?.formatted_address || '',\n\t\t\t\t\t\t\tcityId: '1046,1127,1135',\n\t\t\t\t\t\t\tlabelId: 25,\n\t\t\t\t\t\t\tlng: this.regeocode?.lng || uni.getStorageSync('lng') || 0,\n\t\t\t\t\t\t\tlat: this.regeocode?.lat || uni.getStorageSync('lat') || 0,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tconsole.log('Registering master with:', userinster);\n\t\t\t\t\t\treturn this.$api.shifu.masterEnter(userinster).then(res => {\n\t\t\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\t\t\tconsole.log('Master registration successful');\n\t\t\t\t\t\t\t\treturn this.$api.shifu.getMaster();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthrow new Error('Master registration failed');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}).then(masterRess => {\n\t\t\t\t\t\t\tif (!masterRess || typeof masterRess !== 'object') {\n\t\t\t\t\t\t\t\tthrow new Error('获取师傅信息失败');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tlet masterRes=masterRess.data\n\t\t\t\t\t\t\tconst userInfo = {\n\t\t\t\t\t\t\t\tphone: masterRes.mobile || this.userInfo.phone || uni.getStorageSync('phone') || '',\n\t\t\t\t\t\t\t\tavatarUrl: masterRes.avatarUrl || this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',\n\t\t\t\t\t\t\t\tnickName: masterRes.coachName || this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\t\t\t\tuserId: masterRes.id || this.userInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\t\t\t\tpid: masterRes.pid || this.userInfo.pid || uni.getStorageSync('pid') || ''\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify({\n\t\t\t\t\t\t\t\tmobile: userInfo.phone,\n\t\t\t\t\t\t\t\tavatarUrl: userInfo.avatarUrl,\n\t\t\t\t\t\t\t\tcoachName: userInfo.nickName,\n\t\t\t\t\t\t\t\tid: userInfo.userId,\n\t\t\t\t\t\t\t\tpid: userInfo.pid,\n\t\t\t\t\t\t\t\tstatus: this.shifustatus,\n\t\t\t\t\t\t\t\tmessagePush: Number(masterRes.messagePush) || -1\n\t\t\t\t\t\t\t}));\n\t\t\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\t\t\tval: userInfo\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthis.saveUserInfoToStorage(userInfo);\n\t\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn this.$api.shifu.getMaster().then(masterRess => {\n\t\t\t\t\t\t\tif (!masterRess || typeof masterRess !== 'object') {\n\t\t\t\t\t\t\t\tthrow new Error('获取师傅信息失败');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tlet masterRes=masterRess.data\n\t\t\t\t\t\t\tconst userInfo = {\n\t\t\t\t\t\t\t\tphone: masterRes.mobile || this.userInfo.phone || uni.getStorageSync('phone') || '',\n\t\t\t\t\t\t\t\tavatarUrl: masterRes.avatarUrl || this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',\n\t\t\t\t\t\t\t\tnickName: masterRes.coachName || this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\t\t\t\tuserId: masterRes.id || this.userInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\t\t\t\tpid: masterRes.pid || this.userInfo.pid || uni.getStorageSync('pid') || ''\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify({\n\t\t\t\t\t\t\t\tmobile: userInfo.phone,\n\t\t\t\t\t\t\t\tavatarUrl: userInfo.avatarUrl,\n\t\t\t\t\t\t\t\tcoachName: userInfo.nickName,\n\t\t\t\t\t\t\t\tid: userInfo.userId,\n\t\t\t\t\t\t\t\tpid: userInfo.pid,\n\t\t\t\t\t\t\t\tstatus: this.shifustatus,\n\t\t\t\t\t\t\t\tmessagePush: Number(masterRes.messagePush) || -1\n\t\t\t\t\t\t\t}));\n\t\t\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\t\t\tval: userInfo\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthis.saveUserInfoToStorage(userInfo);\n\t\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('getshifuinfo error:', err);\n\t\t\t\t\tthis.shifustatus = -1;\n\t\t\t\t\tconst defaultUserInfo = {\n\t\t\t\t\t\tphone: this.userInfo.phone || uni.getStorageSync('phone') || '',\n\t\t\t\t\t\tavatarUrl: this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',\n\t\t\t\t\t\tnickName: this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\t\tuserId: this.userInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\t\tpid: this.userInfo.pid || uni.getStorageSync('pid') || ''\n\t\t\t\t\t};\n\t\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify({\n\t\t\t\t\t\tmobile: defaultUserInfo.phone,\n\t\t\t\t\t\tavatarUrl: defaultUserInfo.avatarUrl,\n\t\t\t\t\t\tcoachName: defaultUserInfo.nickName,\n\t\t\t\t\t\tid: defaultUserInfo.userId,\n\t\t\t\t\t\tpid: defaultUserInfo.pid,\n\t\t\t\t\t\tstatus: -1,\n\t\t\t\t\t\tmessagePush: -1\n\t\t\t\t\t}));\n\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\tval: defaultUserInfo\n\t\t\t\t\t});\n\t\t\t\t\tthis.saveUserInfoToStorage(defaultUserInfo);\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\tdebounceGetHighlight: debounce(function() {\n\t\t\t\tthis.getHighlight();\n\t\t\t}, 300),\n\t\t\tgetHighlight() {\n\t\t\t\tconst userId = this.userInfo.userId || uni.getStorageSync('userId');\n\t\t\t\tif (!userId) {\n\t\t\t\t\tconsole.log('No userId, skipping getHighlight');\n\t\t\t\t\treturn Promise.resolve();\n\t\t\t\t}\n\t\t\t\tthis.isLoading = true;\n\t\t\t\treturn this.$api.service.getHighlight({\n\t\t\t\t\tuserId: userId,\n\t\t\t\t\trole: 1\n\t\t\t\t}).then(res => {\n\t\t\t\t\tconsole.log('getHighlight response:', res);\n\t\t\t\t\tconst updatedOrderList = this.orderList.map((item, index) => ({\n\t\t\t\t\t\t...item,\n\t\t\t\t\t\tcount: index === 0 ? (res && res.countOrder ? res.countOrder : 0) :\n\t\t\t\t\t\t\tindex === 1 ? (res && res.shiFuBaoJia ? res.shiFuBaoJia : 0) :\n\t\t\t\t\t\t\tindex === 2 ? (res && res.daiZhiFu ? res.daiZhiFu : 0) :\n\t\t\t\t\t\t\tindex === 3 ? (res && res.daiFuWu ? res.daiFuWu : 0) :\n\t\t\t\t\t\t\tindex === 4 ? (res && res.fuWuZhong ? res.fuWuZhong : 0) :\n\t\t\t\t\t\t\tindex === 5 ? (res && res.yiWanCheng ? res.yiWanCheng : 0) : 0\n\t\t\t\t\t}));\n\t\t\t\t\tthis.$set(this, 'orderList', updatedOrderList);\n\t\t\t\t}).finally(() => {\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t});\n\t\t\t},\n\t\t\thandleContact(e) {\n\t\t\t\tconsole.log(e.detail.path);\n\t\t\t\tconsole.log(e.detail.query);\n\t\t\t},\n\t\t\t...mapMutations(['updateUserItem', 'setRegeocode']),\n\t\t\tshowLoginPopup() {\n\t\t\t\tthis.loginPopupVisible = true;\n\t\t\t},\n\t\t\thideLoginPopup() {\n\t\t\t\tthis.loginPopupVisible = false;\n\t\t\t\tthis.agreedToTerms = false;\n\t\t\t},\n\t\t\ttoggleAgreement() {\n\t\t\t\tthis.agreedToTerms = !this.agreedToTerms;\n\t\t\t},\n\t\t\tnavigateToAgreement(type) {\n\t\t\t\tlet url = '../user/configuser';\n\t\t\t\tif (type === 'service') {\n\t\t\t\t\turl += '?type=service';\n\t\t\t\t} else if (type === 'privacy') {\n\t\t\t\t\turl += '?type=privacy';\n\t\t\t\t}\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: url\n\t\t\t\t});\n\t\t\t},\n\t\t\tinitUserData() {\n\t\t\t\tif (this.token && !this.userInfo.phone) {\n\t\t\t\t\tconst userInfo = {\n\t\t\t\t\t\tphone: uni.getStorageSync('phone') || '',\n\t\t\t\t\t\tavatarUrl: uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',\n\t\t\t\t\t\tnickName: uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\t\tuserId: uni.getStorageSync('userId') || '',\n\t\t\t\t\t\tpid: uni.getStorageSync('pid') || ''\n\t\t\t\t\t};\n\t\t\t\t\tif (userInfo.phone) {\n\t\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\t\tval: userInfo\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.saveUserInfoToStorage(userInfo);\n\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.handleInvalidSession();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tnavigateTo(url) {\n\t\t\t\tif (!url) return;\n\t\t\t\tconst requiresLogin = [\n\t\t\t\t\t// '../user/coupon', // These were commented out in the original code\n\t\t\t\t\t// '../user/repair_record',\n\t\t\t\t\t// '../user/order_list',\n\t\t\t\t\t// '../user/address',\n\t\t\t\t\t// '../user/Settle',\n\t\t\t\t\t// '../user/agent_apply',\n\t\t\t\t\t// '../user/promotion',\n\t\t\t\t\t// '../user/bankCard',\n\t\t\t\t\t// '../shifu/Settle',\n\t\t\t\t\t// '../shifu/Receiving',\n\t\t\t\t\t// '../shifu/mine'\n\t\t\t\t];\n\t\t\t\tif (requiresLogin.some(path => url.startsWith(path)) && !this.isLoggedIn) {\n\t\t\t\t\treturn this.showToast('请先登录');\n\t\t\t\t}\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl\n\t\t\t\t});\n\t\t\t},\n\t\t\tsaveUserInfoToStorage(userInfo) {\n\t\t\t\tuni.setStorageSync('phone', userInfo.phone || '');\n\t\t\t\tuni.setStorageSync('avatarUrl', userInfo.avatarUrl || '');\n\t\t\t\tuni.setStorageSync('nickName', userInfo.nickName || '');\n\t\t\t\tuni.setStorageSync('userId', userInfo.userId || '');\n\t\t\t\tuni.setStorageSync('pid', userInfo.pid || '');\n\t\t\t},\n\t\t\thandleInvalidSession() {\n\t\t\t\t['token', 'phone', 'avatarUrl', 'nickName', 'userId', 'pid'].forEach(key => {\n\t\t\t\t\tuni.removeStorageSync(key);\n\t\t\t\t});\n\t\t\t\tuni.removeStorageSync('shiInfo');\n\t\t\t\tthis.updateUserItem({\n\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\tval: {}\n\t\t\t\t});\n\t\t\t\tthis.updateUserItem({\n\t\t\t\t\tkey: 'autograph',\n\t\t\t\t\tval: ''\n\t\t\t\t});\n\t\t\t\tthis.isLoading = false;\n\t\t\t\tthis.$set(this, 'orderList', this.orderList.map(item => ({\n\t\t\t\t\t...item,\n\t\t\t\t\tcount: 0\n\t\t\t\t})));\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t});\n\t\t\t},\n\t\t\tonGetPhoneNumber(e) {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/login'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t\t// #endif\n\t\t\t\tif (e.detail.errMsg !== 'getPhoneNumber:ok') {\n\t\t\t\t\treturn this.showToast('授权失败，请重试');\n\t\t\t\t}\n\t\t\t\tthis.getmylogin();\n\t\t\t\tthis.isLoading = true;\n\t\t\t\tuni.showLoading({\n\t\t\t\t\tmask: true,\n\t\t\t\t\ttitle: '登录中...'\n\t\t\t\t});\n\t\t\t\tconst {\n\t\t\t\t\tencryptedData,\n\t\t\t\t\tiv\n\t\t\t\t} = e.detail;\n\t\t\t\tuni.checkSession({\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tthis.loginWithWeixin({\n\t\t\t\t\t\t\tcode: this.code,\n\t\t\t\t\t\t\tencryptedData,\n\t\t\t\t\t\t\tiv,\n\t\t\t\t\t\t\tpid: this.inviteCode\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tfail: () => {\n\t\t\t\t\t\tuni.login({\n\t\t\t\t\t\t\tprovider: 'weixin',\n\t\t\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\t\t\tif (res.code) {\n\t\t\t\t\t\t\t\t\tthis.code = res.code;\n\t\t\t\t\t\t\t\t\tconsole.log('Refreshed wx.login code:', this.code);\n\t\t\t\t\t\t\t\t\tthis.loginWithWeixin({\n\t\t\t\t\t\t\t\t\t\tcode: this.code,\n\t\t\t\t\t\t\t\t\t\tencryptedData,\n\t\t\t\t\t\t\t\t\t\tiv,\n\t\t\t\t\t\t\t\t\t\tpid: this.inviteCode\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\tthis.showToast('获取登录凭证失败');\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tthis.showToast('微信登录失败，请重试');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tasync loginWithWeixin(params) {\n\t\t\t\ttry {\n\t\t\t\t\tconst response = await this.$api.user.loginuserInfo({\n\t\t\t\t\t\tcode: params.code,\n\t\t\t\t\t\tencryptedData: params.encryptedData,\n\t\t\t\t\t\tiv: params.iv,\n\t\t\t\t\t\tpid: this.inviteCode\n\t\t\t\t\t});\n\t\t\t\t\tconsole.log(response)\n\t\t\t\t\tif (!response || !response.data.token) {\n\t\t\t\t\t\tthrow new Error('请重新登录');\n\t\t\t\t\t}\n\t\t\t\t\tuni.setStorageSync('token', response.data.token);\n\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\tkey: 'autograph',\n\t\t\t\t\t\tval: response.data.token\n\t\t\t\t\t});\n\t\t\t\t\tconst userInfoRes = await this.$api.user.userInfo();\n\t\t\t\t\tif (!userInfoRes || typeof userInfoRes !== 'object') {\n\t\t\t\t\t\tthrow new Error('获取用户信息失败');\n\t\t\t\t\t}\n\t\t\t\t\tconsole.log(userInfoRes)\n\t\t\t\t\tconst initialUserInfo = {\n\t\t\t\t\t\tphone: userInfoRes.phone || '',\n\t\t\t\t\t\tavatarUrl: userInfoRes.avatarUrl || '/static/mine/default_user.png',\n\t\t\t\t\t\tnickName: userInfoRes.nickName || '微信用户',\n\t\t\t\t\t\tuserId: userInfoRes.id || '',\n\t\t\t\t\t\tpid: userInfoRes.pid || ''\n\t\t\t\t\t};\n\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\tval: initialUserInfo\n\t\t\t\t\t});\n\t\t\t\t\tthis.saveUserInfoToStorage(initialUserInfo);\n\t\t\t\t\tconst shifuStatusRes = await this.$api.shifu.getshifstutas({\n\t\t\t\t\t\tuserId: initialUserInfo.userId\n\t\t\t\t\t});\n\t\t\t\t\tthis.shifustatus = Number(shifuStatusRes.data) !== undefined ? Number(shifuStatusRes.data) : -1;\n\t\t\t\t\tif (shifuStatusRes.data === -1) {\n\t\t\t\t\t\tconst userinster = {\n\t\t\t\t\t\t\tuserId: initialUserInfo.userId,\n\t\t\t\t\t\t\tmobile: initialUserInfo.phone,\n\t\t\t\t\t\t\taddress: this.regeocode?.regeocode?.formatted_address || '',\n\t\t\t\t\t\t\tcityId: '1046,1127,1135',\n\t\t\t\t\t\t\tlabelId: 25,\n\t\t\t\t\t\t\tlng: this.regeocode?.lng || uni.getStorageSync('lng') || 0,\n\t\t\t\t\t\t\tlat: this.regeocode?.lat || uni.getStorageSync('lat') || 0,\n\t\t\t\t\t\t};\n\t\t\t\t\t\tconsole.log('Registering master with:', userinster);\n\t\t\t\t\t\tconst registerRes = await this.$api.shifu.masterEnter(userinster);\n\t\t\t\t\t\tif (registerRes.code !== \"200\") {\n\t\t\t\t\t\t\tthrow new Error('Master registration failed');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.log('Master registration successful');\n\t\t\t\t\t}\n\t\t\t\t\tconst masterRess = await this.$api.shifu.getMaster();\n\t\t\t\t\tif (!masterRess || typeof masterRess !== 'object') {\n\t\t\t\t\t\tthrow new Error('获取师傅信息失败');\n\t\t\t\t\t}\n\t\t\t\t\tlet masterRes=masterRess.data\n\t\t\t\t\tconst userInfo = {\n\t\t\t\t\t\tphone: masterRes.mobile || initialUserInfo.phone || '',\n\t\t\t\t\t\tavatarUrl: masterRes.avatarUrl || initialUserInfo.avatarUrl || '/static/mine/default_user.png',\n\t\t\t\t\t\tnickName: masterRes.coachName || initialUserInfo.nickName || '微信用户',\n\t\t\t\t\t\tuserId: masterRes.userId || '',\n\t\t\t\t\t\tshufuId: masterRes.id  || '',\n\t\t\t\t\t\tpid: masterRes.pid || initialUserInfo.pid || ''\n\t\t\t\t\t};\n\t\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify({\n\t\t\t\t\t\tmobile: userInfo.phone,\n\t\t\t\t\t\tavatarUrl: userInfo.avatarUrl,\n\t\t\t\t\t\tcoachName: userInfo.nickName,\n\t\t\t\t\t\tshufuId: userInfo.shufuId,\n\t\t\t\t\t\tuserId: userInfo.userId,\n\t\t\t\t\t\n\t\t\t\t\t\tpid: userInfo.pid,\n\t\t\t\t\t\tstatus: this.shifustatus,\n\t\t\t\t\t\tmessagePush: Number(masterRes.messagePush) || -1\n\t\t\t\t\t}));\n\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\tval: userInfo\n\t\t\t\t\t});\n\t\t\t\t\tthis.saveUserInfoToStorage(userInfo);\n\t\t\t\t\tconst modalShownKey = `certificationModalShown_${userInfo.userId}_${this.shifustatus}`;\n\t\t\t\t\tconst hasShownModal = uni.getStorageSync(modalShownKey);\n\t\t\t\t\tif (!hasShownModal && (this.shifustatus === -1 || this.shifustatus === 4)) {\n\t\t\t\t\t\tthis.showCertificationPopup();\n\t\t\t\t\t\tuni.setStorageSync(modalShownKey, 'true');\n\t\t\t\t\t}\n\t\t\t\t\tthis.showToast('登录成功', 'success');\n\t\t\t\t\tthis.hideLoginPopup();\n\t\t\t\t\tthis.debounceGetHighlight();\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Login error:', error);\n\t\t\t\t\tthis.showToast(error.message || '登录失败，请稍后重试');\n\t\t\t\t\tthis.handleInvalidSession();\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\tshowToast(title, icon = 'none') {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle,\n\t\t\t\t\ticon,\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t},\n\t\t\tasync fetchShifuInfo() {\n\t\t\t\ttry {\n\t\t\t\t\tthis.isLoading = true;\n\t\t\t\t\tconst shiInfoResponses = await this.$api.shifu.getMaster();\n\t\t\t\t\tlet shiInfoResponse=shiInfoResponses.data\n\t\t\t\t\tconsole.log('fetchShifuInfo response:', shiInfoResponse);\n\t\t\t\t\tif (!shiInfoResponses || typeof shiInfoResponses !== 'object') {\n\t\t\t\t\t\tthrow new Error('获取师傅状态失败: 响应数据无效');\n\t\t\t\t\t}\n\t\t\t\t\tthis.shifustatus = Number(shiInfoResponse.status) !== undefined ? Number(shiInfoResponse.status) : -1;\n\t\t\t\t\tconst userInfo = {\n\t\t\t\t\t\tphone: shiInfoResponse.mobile || this.userInfo.phone || uni.getStorageSync('phone') || '',\n\t\t\t\t\t\tavatarUrl: shiInfoResponse.avatarUrl || this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',\n\t\t\t\t\t\tnickName: shiInfoResponse.coachName || this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\t\tshifuId: shiInfoResponse.id || this.userInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\t\tuserId: shiInfoResponse.userId|| this.userInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\t\tpid: shiInfoResponse.pid || this.userInfo.pid || uni.getStorageSync('pid') || ''\n\t\t\t\t\t};\n\t\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify({\n\t\t\t\t\t\tmobile: userInfo.phone,\n\t\t\t\t\t\tavatarUrl: userInfo.avatarUrl,\n\t\t\t\t\t\tcoachName: userInfo.nickName,\n\t\t\t\t\t\tuserId: userInfo.userId,\n\t\t\t\t\t\tshifuId: userInfo.shifuId,\n\t\t\t\t\t\tpid: userInfo.pid,\n\t\t\t\t\t\tstatus: this.shifustatus,\n\t\t\t\t\t\tmessagePush: Number(shiInfoResponse.messagePush) || -1\n\t\t\t\t\t}));\n\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\tval: userInfo\n\t\t\t\t\t});\n\t\t\t\t\tconst modalShownKey = `certificationModalShown_${userInfo.userId}_${this.shifustatus}`;\n\t\t\t\t\tconst hasShownModal = uni.getStorageSync(modalShownKey);\n\t\t\t\t\tif (!hasShownModal && (this.shifustatus === -1 || this.shifustatus === 4)) {\n\t\t\t\t\t\tthis.showCertificationPopup();\n\t\t\t\t\t\tuni.setStorageSync(modalShownKey, 'true');\n\t\t\t\t\t}\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('fetchShifuInfo error:', error);\n\t\t\t\t\tthis.shifustatus = -1;\n\t\t\t\t\tconst defaultUserInfo = {\n\t\t\t\t\t\tphone: this.userInfo.phone || uni.getStorageSync('phone') || '',\n\t\t\t\t\t\tavatarUrl: this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',\n\t\t\t\t\t\tnickName: this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\t\tuserId: this.userInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\t\tpid: this.userInfo.pid || uni.getStorageSync('pid') || ''\n\t\t\t\t\t};\n\t\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify({\n\t\t\t\t\t\tmobile: defaultUserInfo.phone,\n\t\t\t\t\t\tavatarUrl: defaultUserInfo.avatarUrl,\n\t\t\t\t\t\tcoachName: defaultUserInfo.nickName,\n\t\t\t\t\t\tuserId: userInfo.userId,\n\t\t\t\t\t\tshifuId: userInfo.shifuId,\n\t\t\t\t\t\tpid: defaultUserInfo.pid,\n\t\t\t\t\t\tstatus: -1,\n\t\t\t\t\t\tmessagePush: -1\n\t\t\t\t\t}));\n\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\tval: defaultUserInfo\n\t\t\t\t\t});\n\t\t\t\t\tconst modalShownKey = `certificationModalShown_${defaultUserInfo.userId}_${defaultUserInfo.status}`;\n\t\t\t\t\tconst hasShownModal = uni.getStorageSync(modalShownKey);\n\t\t\t\t\tif (!hasShownModal && defaultUserInfo.status === -1) {\n\t\t\t\t\t\tthis.showCertificationPopup();\n\t\t\t\t\t\tuni.setStorageSync(modalShownKey, 'true');\n\t\t\t\t\t}\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t}\n\t\t\t},\n\t\t\tgetshifustatus() {\n\t\t\t\tconst shiInfo = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};\n\t\t\t\tthis.shifustatus = shiInfo.status;\n\t\t\t\tconsole.log('getshifustatus:', this.shifustatus);\n\t\t\t},\n\t\t\tshowCertificationPopup() {\n\t\t\t\tconsole.log('showCertificationPopup called, current shifustatus:', this.shifustatus);\n\t\t\t\tif (this.shifustatus === -1 || this.shifustatus === 4) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: this.shifustatus === -1 ? '您尚未成为师傅，是否前往认证？' : '您的师傅认证被驳回，是否重新认证？',\n\t\t\t\t\t\tconfirmText: '去认证',\n\t\t\t\t\t\tcancelText: '再想想',\n\t\t\t\t\t\tcancelable: true,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tconst targetUrl = '/shifu/Settle';\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: targetUrl,\n\t\t\t\t\t\t\t\t\tfail(err) {\n\t\t\t\t\t\t\t\t\t\tconsole.error('Navigation to certification failed:', err);\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '跳转认证页面失败',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('Modal failed:', err);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\thandleNavigate(url) {\n\t\t\t\tconst directNavigatePaths = ['/shifu/Settle', '/user/promotion', '/shifu/master_Info'];\n\n\t\t\t\tif (directNavigatePaths.includes(url)) {\n\t\t\t\t\tthis.navigateTo(url);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (this.shifustatus === -1 || this.shifustatus === 4) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '你还不是师傅',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tthis.showCertificationPopup();\n\t\t\t\t} else if (this.shifustatus === 1) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '师傅状态在审核中',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} else if (this.shifustatus === 2) {\n\t\t\t\t\tthis.navigateTo(url);\n\t\t\t\t} else {\n                    this.navigateTo(url); // Fallback to navigate if status is unexpected.\n                }\n\t\t\t},\n\t\t\thandleCallKf() {\n\t\t\t\tif (this.shifustatus === -1 || this.shifustatus === 4) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '你还不是师傅',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tthis.showCertificationPopup();\n\t\t\t\t} else if (this.shifustatus === 1) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '师傅状态在审核中',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} else if (this.shifustatus === 2) {\n\t\t\t\t\tthis.callkf();\n\t\t\t\t}\n\t\t\t},\n\t\t\tcallkf() {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '联系客服功能待实现',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\">\n\t/* Login Popup Styles */\n\t.login-popup-overlay {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tz-index: 2000;\n\t\tdisplay: flex;\n\t\talign-items: flex-end;\n\t\tjustify-content: center;\n\t}\n\n\t.login-popup {\n\t\tbackground-color: #fff;\n\t\twidth: 100%;\n\t\tborder-radius: 40rpx 40rpx 0 0;\n\t\tposition: relative;\n\t\tmax-height: 60vh;\n\t\tpadding-bottom: 10rpx;\n\t\tanimation: slideUp 0.3s ease-out;\n\t}\n\n\t@keyframes slideUp {\n\t\tfrom {\n\t\t\ttransform: translateY(100%);\n\t\t}\n\n\t\tto {\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t.close-btn {\n\t\tposition: absolute;\n\t\ttop: 30rpx;\n\t\tright: 30rpx;\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: #999;\n\t\tfont-size: 40rpx;\n\t\tz-index: 10;\n\t}\n\n\t.popup-content {\n\t\tpadding: 80rpx 60rpx 40rpx;\n\t\ttext-align: center;\n\t}\n\n\t.welcome-title {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.welcome-subtitle {\n\t\tfont-size: 32rpx;\n\t\tcolor: #666;\n\t\tmargin-bottom: 80rpx;\n\t}\n\n\t.agreement-section {\n\t\tmargin-bottom: 60rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n\n\t.checkbox-container {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\ttext-align: left;\n\t\tmax-width: 560rpx;\n\t}\n\n\t.checkbox {\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t\tborder: 2rpx solid #ddd;\n\t\tborder-radius: 6rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-right: 20rpx;\n\t\tmargin-top: 4rpx;\n\t\tflex-shrink: 0;\n\t\tbackground-color: #fff;\n\t\ttransition: all 0.2s;\n\n\t\t&.checked {\n\t\t\tbackground-color: #00C853;\n\t\t\tborder-color: #00C853;\n\t\t\tcolor: #fff;\n\t\t}\n\n\t\t.iconfont {\n\t\t\tfont-size: 24rpx;\n\t\t}\n\t}\n\n\t.agreement-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tline-height: 1.6;\n\n\t\t.link {\n\t\t\tcolor: #00C853;\n\t\t}\n\t}\n\n\t.phone-login-btn {\n\t\twidth: 100%;\n\t\theight: 100rpx;\n\t\tbackground: linear-gradient(135deg, #00C853, #4CAF50);\n\t\tborder: none;\n\t\tborder-radius: 50rpx;\n\t\tcolor: #fff;\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 60rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbox-shadow: 0 8rpx 20rpx rgba(0, 200, 83, 0.3);\n\t\ttransition: all 0.2s;\n\n\t\t&:active:not(.disabled) {\n\t\t\ttransform: translateY(2rpx);\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 200, 83, 0.3);\n\t\t}\n\n\t\t&.disabled {\n\t\t\tbackground: #ccc;\n\t\t\tbox-shadow: none;\n\t\t\topacity: 0.6;\n\t\t}\n\n\t\t&::after {\n\t\t\tborder: none;\n\t\t}\n\t}\n\n\t.alternative-login {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 40rpx;\n\n\t\t.divider-line {\n\t\t\tflex: 1;\n\t\t\theight: 1rpx;\n\t\t\tbackground-color: #eee;\n\t\t}\n\n\t\t.divider-text {\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #999;\n\t\t\tmargin: 0 30rpx;\n\t\t}\n\t}\n\n\t.sms-login-btn {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tbackground-color: #fff;\n\t\tborder: 2rpx solid #ddd;\n\t\tborder-radius: 44rpx;\n\t\tcolor: #666;\n\t\tfont-size: 32rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.2s;\n\n\t\t&:active {\n\t\t\tbackground-color: #f8f8f8;\n\t\t\tborder-color: #00C853;\n\t\t}\n\n\t\t&::after {\n\t\t\tborder: none;\n\t\t}\n\n\t\t.iconfont {\n\t\t\tmargin-right: 16rpx;\n\t\t\tfont-size: 36rpx;\n\t\t}\n\t}\n\n\t/* Floating Contact Button Styles */\n\t.floating-contact {\n\t\tposition: fixed;\n\t\tbottom: 150rpx;\n\t\tright: 30rpx;\n\t\tz-index: 1000;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 50rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n\t\tpadding: 10rpx 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.contact-container {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.contact-btn {\n\t\tbackground: none;\n\t\tborder: none;\n\t\tcolor: #576b95;\n\t\tfont-size: 30rpx;\n\t\tline-height: 1.5;\n\t\tpadding: 10rpx 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.contact-btn:active {\n\t\tbackground-color: #ededee;\n\t}\n\n\t/* Existing Styles */\n\t.pages-mine {\n\t\tbackground-color: #f8f8f8;\n\t\tmin-height: 100vh;\n\t\tpadding-bottom: 120rpx;\n\n\t\t.header {\n\t\t\theight: 292rpx;\n\t\t\tbackground-color: #599EFF;\n\t\t\tposition: relative;\n\n\t\t\t.header-content {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 40rpx 30rpx 0;\n\n\t\t\t\t.avatar_view {\n\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\theight: 120rpx;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\tbox-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n\n\t\t\t\t\t.avatar {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.user-info {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\tmargin-left: 20rpx;\n\t\t\t\t\tcolor: #fff;\n\n\t\t\t\t\t.user-info-logged {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tflex-direction: column;\n\t\t\t\t\t\tgap: 10rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.nickname {\n\t\t\t\t\t\tfont-size: 36rpx;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t}\n\n\t\t\t\t\t.phone-number {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\topacity: 0.9;\n\t\t\t\t\t}\n\n\t\t\t\t\tbutton {\n\t\t\t\t\t\tbackground: none;\n\t\t\t\t\t\tborder: 2rpx solid rgba(255, 255, 255, 0.5);\n\t\t\t\t\t\tborder-radius: 32rpx;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\tline-height: 1.5;\n\t\t\t\t\t\tpadding: 10rpx 30rpx;\n\n\t\t\t\t\t\t&.loading {\n\t\t\t\t\t\t\topacity: 0.7;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&::after {\n\t\t\t\t\t\t\tborder: none;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.status-badge {\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\tpadding: 8rpx 20rpx;\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tline-height: 1.2;\n\t\t\t\t\t\tborder-radius: 20rpx;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\tmargin-top: 10rpx;\n\t\t\t\t\t\twidth: fit-content;\n\t\t\t\t\t\tbox-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\n\t\t\t\t\t}\n\n\t\t\t\t\t.status-not-registered {\n\t\t\t\t\t\tbackground-color: #b0b0b0;\n\t\t\t\t\t}\n\n\t\t\t\t\t.status-pending {\n\t\t\t\t\t\tbackground-color: #f4b400;\n\t\t\t\t\t}\n\n\t\t\t\t\t.status-approved {\n\t\t\t\t\t\tbackground-color: #f5a623;\n\t\t\t\t\t}\n\n\t\t\t\t\t.status-rejected {\n\t\t\t\t\t\tbackground-color: #f44336;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.settings {\n\t\t\t\t\tpadding: 10rpx;\n\n\t\t\t\t\t.icon-xitong {\n\t\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.box1 {\n\t\t\tmargin-top: -20rpx;\n\t\t\tborder-radius: 36rpx 36rpx 0 0;\n\t\t\tposition: relative;\n\t\t\tz-index: 10;\n\t\t}\n\n\t\t.mine-menu-list {\n\t\t\tbackground-color: #fff;\n\t\t\tmargin: 0 20rpx;\n\t\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t.menu-title {\n\t\t\t\theight: 90rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 0 30rpx 0 40rpx;\n\t\t\t\tborder-bottom: 1px solid #f0f0f0;\n\n\t\t\t\t.f-paragraph {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.flex-warp {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\tpadding: 30rpx 0;\n\n\t\t\t\t.order-item {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\twidth: 25%;\n\t\t\t\t\tfont-size: 25rpx;\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\ttransition: transform 0.2s;\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t\t}\n\n\t\t\t\t\t.icon-container {\n\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t}\n\n\t\t\t\t\t.number-circle {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: -10rpx;\n\t\t\t\t\t\tright: -5rpx;\n\t\t\t\t\t\twidth: 30rpx;\n\t\t\t\t\t\theight: 30rpx;\n\t\t\t\t\t\tbackground-color: #ff4d4f;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.mt-sm {\n\t\t\t\t\tmargin-top: 16rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.spacer {\n\t\t\theight: 20rpx;\n\t\t\tbackground-color: transparent;\n\t\t}\n\n\t\t.mine-tool-grid {\n\t\t\tbackground-color: #fff;\n\t\t\tmargin: 0 20rpx 30rpx;\n\t\t\tborder-radius: 12rpx;\n\t\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t\t\tpadding: 30rpx;\n\n\t\t\t.grid-container {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\tjustify-content: flex-start;\n\t\t\t\tgap: 20rpx;\n\t\t\t}\n\n\t\t\t.grid-item {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\twidth: calc(33.33% - 20rpx);\n\t\t\t\tmin-width: 140rpx;\n\t\t\t\ttransition: transform 0.2s ease;\n\n\t\t\t\t&:active {\n\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t}\n\n\t\t\t\t.grid-icon-container {\n\t\t\t\t\twidth: 80rpx;\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t\tborder-radius: 20rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tposition: relative;\n\n\t\t\t\t\t&.switch-identity {\n\t\t\t\t\t\t/* Specific styling for switch-identity icon */\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.grid-text {\n\t\t\t\t\tfont-size: 25rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tline-height: 1.2;\n\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t}\n\n\t\t\t\t.contact-btn-wrapper {\n\t\t\t\t\tbackground: none;\n\t\t\t\t\tborder: none;\n\t\t\t\t\tpadding: 0;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tline-height: 1;\n\t\t\t\t\t&::after {\n\t\t\t\t\t\tborder: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.flex-between {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t}\n\t}\n</style>\n```", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755661912152\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}