{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/userProfile.vue?83c5", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/userProfile.vue?6df7", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/userProfile.vue?e79f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/userProfile.vue?5f30", "uni-app:///shifu/userProfile.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/userProfile.vue?e129", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/userProfile.vue?e5ef"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "avatarUrl", "localNickName", "originalUserInfo", "nick<PERSON><PERSON>", "shi<PERSON><PERSON>", "messagePush", "value", "showSubscribeModal", "onLoad", "onShow", "methods", "getSInfo", "res", "console", "checkMessagePushStatus", "withSubscriptions", "settingRes", "hasAcceptedSubscription", "obj", "set", "uni", "success", "fail", "title", "icon", "change", "val", "messagePushStatus", "id", "goToSubscriptionSettings", "loadUserInfo"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA01B,CAAgB,02BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCyC92B;EACAC;IACA;MACAC;QACAC;MACA;;MACAC;MAAA;MACAC;QACAF;QACAG;MACA;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IAGA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBACA;kBACA;gBACA;kBACA;gBACA;gBACAC;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEArB;kBAAAsB;gBAAA;cAAA;gBAAAC;gBACAC;gBAEA;kBACAC,oDACA;kBACAD;oBAAA;kBAAA;gBACA;gBAEA;kBACA;kBACA;kBACA;kBACA;kBACAJ;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACAM;MACAC;QACAC;UACAR;QACA;QACAS;UACAT;UACAO;YAAAG;YAAAC;UAAA;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAZ;gBACA;gBAAA,KAEAa;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAGAjC;kBAAAsB;gBAAA;cAAA;gBAAAC;gBACAC;gBAEA;kBACAC,oDACA;kBACAD;oBAAA;kBAAA;gBACA;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACAJ;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAA;gBACAO;kBAAAG;kBAAAC;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAKA;gBACA;gBACA;gBACAG;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACAvB;gBACA;cAAA;gBAHAO;gBAIA;kBACAQ;oBAAAG;oBAAAC;kBAAA;kBACA;kBACA;gBACA;kBACAJ;oBAAAG;oBAAAC;kBAAA;kBACA;kBACA;kBACA;gBACA;;gBACAX;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAO;kBAAAG;kBAAAC;gBAAA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAK;MACA;MACAT;QACAL;QAAA;QACAM;UACAR;UACA;QACA;QACAS;UACAT;UACAO;YAAAG;YAAAC;UAAA;QACA;MACA;IACA;IACAM;MACA;MACA;MACAjB;MACA;MACA;MACA;MACA;MACA;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtNA;AAAA;AAAA;AAAA;AAAykD,CAAgB,6hDAAG,EAAC,C;;;;;;;;;;;ACA7lD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/userProfile.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/userProfile.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./userProfile.vue?vue&type=template&id=3c77ab34&\"\nvar renderjs\nimport script from \"./userProfile.vue?vue&type=script&lang=js&\"\nexport * from \"./userProfile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./userProfile.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/userProfile.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=template&id=3c77ab34&\"", "var components\ntry {\n  components = {\n    uSwitch: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-switch/u-switch\" */ \"uview-ui/components/u-switch/u-switch.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showSubscribeModal = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"header-decoration\">\n      <view class=\"decoration-circle circle-1\"></view>\n      <view class=\"decoration-circle circle-2\"></view>\n      <view class=\"decoration-circle circle-3\"></view>\n    </view>\n\n    <view class=\"user-info\">\n      <view class=\"status-section\">\n        <view class=\"status-container\">\n          <view class=\"status-info\">\n            <text class=\"status-label\">开启接单</text>\n            <text class=\"status-desc\">关闭后不可接收订单消息通知</text>\n          </view>\n          <u-switch v-model=\"messagePush\" @change=\"change\" active-color=\"#599eff\"></u-switch>\n        </view>\n      </view>\n\n      <view class=\"button-group\">\n        <button class=\"action-button secondary-button\" @click=\"set\">\n          <text class=\"button-icon\">️</text>\n          <text class=\"button-text\">系统设置</text>\n        </button>\n      </view>\n    </view>\n\n    <u-modal\n      :show=\"showSubscribeModal\"\n      title=\"提示\"\n      content=\"您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收师傅的报价通知。\"\n      showCancelButton\n      cancelText=\"取消\"\n      confirmText=\"去开启\"\n      @confirm=\"goToSubscriptionSettings\"\n      @cancel=\"showSubscribeModal = false\"\n    ></u-modal>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      userInfo: {\n        avatarUrl: '', // Store the temporary or uploaded avatar URL\n      },\n      localNickName: '微信用户', // Store the temporary nickname\n      originalUserInfo: {\n        avatarUrl: '',\n        nickName: '',\n      },\n      shifuid: '',\n      messagePush: false,\n      value: false, // 师傅状态开关 (Note: this seems to be a duplicate of messagePush's purpose, consider unifying)\n      showSubscribeModal: false, // 控制订阅消息弹窗显示\n    };\n  },\n  onLoad() {\n\n\n    this.loadUserInfo();\n    // No need to call getSInfo here for initial switch state, onShow will handle it\n  },\n  onShow() {\n    // When the page is shown or returns from another page (like settings), re-check the status\n    this.checkMessagePushStatus();\n  },\n  methods: {\n    async getSInfo() {\n      // This method fetches the initial state from your backend\n      try {\n        const res = await this.$api.shifu.getSInfo();\n        if (res.messagePush === -1) {\n          this.messagePush = false;\n        } else {\n          this.messagePush = true;\n        }\n        console.log(\"Backend messagePush status:\", this.messagePush);\n        // Also update 'value' if it's meant to sync with messagePush\n        this.value = this.messagePush;\n      } catch (error) {\n        console.error('Failed to get shifu info from backend:', error);\n      }\n    },\n    async checkMessagePushStatus() {\n      // This method checks both backend status AND WeChat subscription status\n      await this.getSInfo(); // First, get the state from your backend\n\n      if (this.messagePush) { // If backend says it's enabled, double-check WeChat settings\n        try {\n          const settingRes = await wx.getSetting({ withSubscriptions: true });\n          let hasAcceptedSubscription = false;\n\n          if (settingRes.subscriptionsSetting.itemSettings) {\n            const obj = settingRes.subscriptionsSetting.itemSettings;\n            // Check if *any* subscription is 'accept' or 'acceptWithForcePush'\n            hasAcceptedSubscription = Object.keys(obj).some((key) => obj[key] === 'accept' || obj[key] === 'acceptWithForcePush');\n          }\n\n          if (!hasAcceptedSubscription) {\n            // If backend says true, but WeChat settings are not, then set messagePush to false locally\n            // This is crucial for reflecting the actual user permission status on the UI\n            this.messagePush = false;\n            this.value = false; // Keep 'value' in sync if used\n            console.log(\"Subscription not accepted, forcing messagePush to false.\");\n            // Optionally, show a toast or modal here to explain why it was turned off\n            // uni.showToast({ title: '请开启订阅消息权限', icon: 'none' });\n          }\n        } catch (error) {\n          console.error('Error checking subscription settings onShow:', error);\n          // If there's an error checking settings, it's safer to assume it's off\n          this.messagePush = false;\n          this.value = false;\n        }\n      }\n    },\n    set() {\n      uni.openSetting({\n        success(res) {\n          console.log('openSetting result (System Settings):', res);\n        },\n        fail(err) {\n          console.error('openSetting fail (System Settings):', err);\n          uni.showToast({ title: '打开系统设置失败', icon: 'error' });\n        }\n      });\n    },\n    async change(val) {\n      console.log('师傅状态变更:', val);\n      this.value = val; // Keep 'value' in sync with the switch's current state\n\n      if (val) {\n        // If enabling, check subscription settings\n        try {\n          const settingRes = await wx.getSetting({ withSubscriptions: true });\n          let hasAcceptedSubscription = false;\n\n          if (settingRes.subscriptionsSetting.itemSettings) {\n            const obj = settingRes.subscriptionsSetting.itemSettings;\n            // Check if *any* subscription is 'accept' or 'acceptWithForcePush'\n            hasAcceptedSubscription = Object.keys(obj).some((key) => obj[key] === 'accept' || obj[key] === 'acceptWithForcePush');\n          }\n\n          if (!hasAcceptedSubscription) {\n            this.showSubscribeModal = true;\n            this.messagePush = false; // Revert switch if no subscription is accepted\n            this.value = false; // Revert 'value' as well\n            console.log(\"Subscription not accepted, showing modal and reverting switch.\");\n            return; // Stop here, wait for user action on modal\n          }\n        } catch (error) {\n          console.error('Error getting subscription settings during switch change:', error);\n          uni.showToast({ title: '获取订阅设置失败', icon: 'error' });\n          this.messagePush = false; // Revert switch on error\n          this.value = false;\n          return;\n        }\n      }\n\n      // If we reach here, it means either:\n      // 1. The user is turning it off.\n      // 2. The user is turning it on AND they have accepted at least one subscription.\n      let messagePushStatus = this.value ? 0 : -1; // Use this.value as it reflects the current switch state\n      try {\n        const res = await this.$api.shifu.updateMessagePush({\n          id: this.shifuid,\n          messagePush: messagePushStatus,\n        });\n        if (res.code === '200') {\n          uni.showToast({ title: res.data, icon: 'success' });\n          // After successful update, ensure messagePush also reflects this.value\n          this.messagePush = this.value;\n        } else {\n          uni.showToast({ title: '请稍后重试', icon: 'error' });\n          // If API call fails, revert the switch to its previous state\n          this.messagePush = !this.value; // Revert the UI switch\n          this.value = !this.value; // Revert the data property\n        }\n        console.log(\"Update messagePush API response:\", res);\n      } catch (error) {\n        console.error('Failed to update message push status:', error);\n        uni.showToast({ title: '更新失败，请稍后重试', icon: 'error' });\n        // If API call fails, revert the switch to its previous state\n        this.messagePush = !this.value; // Revert the UI switch\n        this.value = !this.value; // Revert the data property\n      }\n    },\n    goToSubscriptionSettings() {\n      this.showSubscribeModal = false;\n      uni.openSetting({\n        withSubscriptions: true, // This is crucial for navigating to the subscription settings\n        success(res) {\n          console.log('openSetting success (Subscription Settings):', res);\n          // After returning from settings, onShow will trigger and re-check\n        },\n        fail(err) {\n          console.error('openSetting fail (Subscription Settings):', err);\n          uni.showToast({ title: '跳转设置失败', icon: 'error' });\n        },\n      });\n    },\n    loadUserInfo() {\n      const cachedUserInfos = uni.getStorageSync('shiInfo') || '{}'; // Ensure it defaults to empty object string\n      const cachedUserInfo = JSON.parse(cachedUserInfos);\n      console.log('Cached User Info:', cachedUserInfo);\n      this.shifuid = cachedUserInfo.id || '';\n      this.userInfo.avatarUrl = cachedUserInfo.avatarUrl || '';\n      this.localNickName = cachedUserInfo.coachName || '微信用户';\n      this.originalUserInfo.avatarUrl = this.userInfo.avatarUrl;\n      this.originalUserInfo.nickName = this.localNickName;\n      console.log('Loaded user info from cache:', cachedUserInfo);\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\">\n/* Your existing styles remain unchanged */\n.container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  min-height: 100vh;\n  padding-top: 40rpx;\n  position: relative;\n  overflow: hidden;\n}\n\n.header-decoration {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 200rpx;\n  pointer-events: none;\n\n  .decoration-circle {\n    position: absolute;\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 50%;\n\n    &.circle-1 {\n      width: 120rpx;\n      height: 120rpx;\n      top: -60rpx;\n      right: 100rpx;\n      animation: float 6s ease-in-out infinite;\n    }\n\n    &.circle-2 {\n      width: 80rpx;\n      height: 80rpx;\n      top: 50rpx;\n      right: 300rpx;\n      animation: float 4s ease-in-out infinite reverse;\n    }\n\n    &.circle-3 {\n      width: 60rpx;\n      height: 60rpx;\n      top: 20rpx;\n      left: 80rpx;\n      animation: float 5s ease-in-out infinite;\n    }\n  }\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-20rpx); }\n}\n\n.user-info {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  backdrop-filter: blur(10px);\n  width: 90%;\n  padding: 60rpx 40rpx;\n  border-radius: 30rpx;\n  margin-top: 80rpx;\n  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);\n  position: relative;\n  z-index: 1;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 6rpx;\n    border-radius: 30rpx 30rpx 0 0;\n  }\n}\n\n.status-section {\n  width: 100%;\n  margin-bottom: 60rpx;\n\n  .status-container {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 30rpx;\n    background: linear-gradient(135deg, #f8f9ff 0%, #f0f3ff 100%);\n    border-radius: 20rpx;\n    border: 2rpx solid rgba(89, 158, 255, 0.1);\n\n    .status-info {\n      display: flex;\n      flex-direction: column;\n\n      .status-label {\n        font-size: 32rpx;\n        color: #333;\n        font-weight: 600;\n        margin-bottom: 8rpx;\n      }\n\n      .status-desc {\n        font-size: 24rpx;\n        color: #666;\n      }\n    }\n  }\n}\n\n.button-group {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n\n  .action-button {\n    width: 100%;\n    height: 100rpx;\n    border-radius: 50rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 32rpx;\n    font-weight: 600;\n    transition: all 0.3s ease;\n    position: relative;\n    overflow: hidden;\n    border: none;\n\n    &::before {\n      content: '';\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      width: 0;\n      height: 0;\n      background: rgba(255, 255, 255, 0.3);\n      border-radius: 50%;\n      transition: all 0.3s ease;\n      transform: translate(-50%, -50%);\n    }\n\n    &:active::before {\n      width: 200%;\n      height: 200%;\n    }\n\n    .button-icon {\n      margin-right: 15rpx;\n      font-size: 28rpx;\n    }\n\n    .button-text {\n      position: relative;\n      z-index: 1;\n    }\n\n    &.secondary-button {\n      background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);\n      color: #599eff;\n      border: 2rpx solid rgba(89, 158, 255, 0.2);\n      box-shadow: 0 5rpx 15rpx rgba(89, 158, 255, 0.1);\n\n      &:active {\n        transform: translateY(2rpx);\n        box-shadow: 0 2rpx 8rpx rgba(89, 158, 255, 0.1);\n      }\n    }\n  }\n}\n\n/* 响应式适配 */\n@media screen and (max-width: 750rpx) {\n  .user-info {\n    width: 95%;\n    padding: 40rpx 30rpx;\n  }\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755655245663\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}