{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodongCashier.vue?d14d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodongCashier.vue?9b7a", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodongCashier.vue?1b2d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodongCashier.vue?1bc7", "uni-app:///user/huodongCashier.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodongCashier.vue?a88e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodongCashier.vue?9d7e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentIndex", "id", "infoyouhuij", "price", "goods_id", "type", "tmplIds", "computed", "allprice", "methods", "getCurrentPlatform", "handleAppWechatPay", "console", "uni", "orderInfo", "appid", "noncestr", "package", "partnerid", "prepayid", "timestamp", "sign", "title", "icon", "setTimeout", "url", "handleMiniProgramPay", "timeStamp", "nonceStr", "signType", "paySign", "appId", "success", "fail", "confirmPay", "content", "cancelText", "confirmText", "orderId", "dingyue", "provider", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA61B,CAAgB,62BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCsBj3B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAKA;MAKA;IACA;IAEA;IACAC;MAAA;MACAC;MACAC;QACA;QACAC;MAAA,mEACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,kEACA;QACAT;QACAC;UACAS;UACAC;QACA;QACAC;UACAX;YACAY;UACA;QACA;MACA,+DACA;QACAb;QACA;UACAC;YACAS;YACAC;UACA;QACA;UACAV;YACAS;YACAC;UACA;QACA;MACA,yBACA;IACA;IAEA;IACAG;MACA;QACAC;QAAA;QACAC;QACAX;QACAY;QACAC;MACA;MACAlB;MACAC;QACA;QACAc;QACAC;QACAX;QACAC;QACAW;QACAC;QACAC;QACAC;UACA;UACApB;UACAC;YACAS;YACAC;UACA;UACA;UACAC;YACAX;cACAY;YACA;UACA;QACA;QACAQ;UACA;UACArB;UACAA;UACA;YACAC;cACAS;cACAC;YACA;UACA;YACAV;cACAS;cACAC;YACA;UACA;UACAX;UACAC;YACAS;YACAC;UACA;QACA;MACA;IACA;IAEAW;MAAA;MACArB;QACAS;QACAa;QACAC;QACAC;QACAL;UACA;YACA;YACA;YACApB;YAEA;cACA0B;cACAjC;YACA;cACA;gBACAQ;kBACAS;kBACAC;gBACA;cACA;gBACAX;gBACA;gBACA;gBACAA;gBACAA;gBACAA;gBACAA;gBACAA;gBACAA;;gBAEA;gBACA;kBACA;kBACAA;kBACA;gBACA;kBACA;kBACAA;kBACA;gBACA;kBACA;kBACAA;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA2B;MAAA;MAEA1B;QACA2B;QACAlC;QACA0B;UACA;YAAA;UAAA;UACA;YACAnB;cACAS;cACAC;YACA;UACA;QACA;QACAU,0BACA;MACA;IAEA;EACA;EACAQ;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA7B;cACAA;cACA;cACA;cACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7OA;AAAA;AAAA;AAAA;AAAomD,CAAgB,wjDAAG,EAAC,C;;;;;;;;;;;ACAxnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/huodongCashier.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/huodongCashier.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./huodongCashier.vue?vue&type=template&id=ba180efa&scoped=true&\"\nvar renderjs\nimport script from \"./huodongCashier.vue?vue&type=script&lang=js&\"\nexport * from \"./huodongCashier.vue?vue&type=script&lang=js&\"\nimport style0 from \"./huodongCashier.vue?vue&type=style&index=0&id=ba180efa&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ba180efa\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/huodongCashier.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodongCashier.vue?vue&type=template&id=ba180efa&scoped=true&\"", "var components\ntry {\n  components = {\n    uCountDown: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-count-down/u-count-down\" */ \"uview-ui/components/u-count-down/u-count-down.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.currentIndex = 0\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodongCashier.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodongCashier.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"time\"><text>支付剩余时间</text> <u-count-down :time=\"15 * 60 * 1000\" format=\"mm:ss\"></u-count-down>\n\t\t</view>\n\t\t<span class=\"price\"><span>￥</span>{{allprice}}</span>\n\t\t<view class=\"payCard\">\n\t\t\t<view class=\"left\">\n\t\t\t\t<image src=\"../static/svg/weixinfang.svg\" mode=\"aspectFill\"></image>\n\t\t\t\t<text>微信支付</text>\n\t\t\t</view>\n\t\t\t<view class=\"choose\" :style=\"currentIndex == 0?'background:#2E80FE;border:2rpx solid #2E80FE':''\"\n\t\t\t\t@click=\"currentIndex = 0\">\n\t\t\t\t<u-icon name=\"checkbox-mark\" color=\"#fff\" size=\"16\"></u-icon>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"footer\">\n\t\t\t<view class=\"btn\" @click=\"confirmPay\">确认支付</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcurrentIndex: 0,\n\t\t\t\tid: '',\n\t\t\t\tinfoyouhuij: '',\n\t\t\t\tprice: '',\n\t\t\t\tgoods_id: '',\n\t\t\t\ttype: 0,\n\t\t\t\ttmplIds: 'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY',\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tallprice() {\n\t\t\t\tconst result = this.price * 1;\n\t\t\t\treturn result <= 0 ? 0.01 : Number(result.toFixed(2));\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 检查当前平台\n\t\t\tgetCurrentPlatform() {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\treturn 'app-plus';\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\treturn 'mp-weixin';\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef H5\n\t\t\t\treturn 'h5';\n\t\t\t\t// #endif\n\t\t\t\treturn 'unknown';\n\t\t\t},\n\n\t\t\t// APP微信支付处理\n\t\t\thandleAppWechatPay(obj) {\n\t\t\t\tconsole.log(111)\n\t\t\t\tuni.requestPayment({\n\t\t\t\t\t\"provider\": \"wxpay\",\n\t\t\t\t\t    orderInfo: 'orderInfo',\n\t\t\t\t\torderInfo: {\n\t\t\t\t\t\tappid: obj.appId,\n\t\t\t\t\t\tnoncestr: obj.nonceStr,\n\t\t\t\t\t\tpackage: 'Sign=WXPay',\n\t\t\t\t\t\tpartnerid: obj.partnerId,\n\t\t\t\t\t\tprepayid: obj.prepayId,\n\t\t\t\t\t\ttimestamp: String(obj.timestamp),\n\t\t\t\t\t\tsign: obj.sign\n\t\t\t\t\t},\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log('APP微信支付成功', res);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t})\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t    url: '/user/tiaozhuan'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('APP微信支付失败:', err);\n\t\t\t\t\t\tif (err.errMsg && err.errMsg.includes('cancel')) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '您已取消支付',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 微信小程序支付处理（保持原有逻辑）\n\t\t\thandleMiniProgramPay(obj) {\n\t\t\t\tconst paymentParams = {\n\t\t\t\t\ttimeStamp: String(obj.timestamp), // 一定要是 string\n\t\t\t\t\tnonceStr: obj.nonceStr,\n\t\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\n\t\t\t\t\tsignType: 'MD5',\n\t\t\t\t\tpaySign: obj.sign\n\t\t\t\t};\n\t\t\t\tconsole.log(JSON.stringify(paymentParams));\n\t\t\t\tuni.requestPayment({\n\t\t\t\t\t\"provider\": 'wxpay',\n\t\t\t\t\ttimeStamp: String(obj.timestamp),\n\t\t\t\t\tnonceStr: obj.nonceStr,\n\t\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\n\t\t\t\t\tpartnerid: obj.partnerId,\n\t\t\t\t\tsignType: \"MD5\",\n\t\t\t\t\tpaySign: obj.sign,\n\t\t\t\t\tappId: obj.appId,\n\t\t\t\t\tsuccess: (res1) => {\n\t\t\t\t\t\t// 支付成功回调\n\t\t\t\t\t\tconsole.log('支付成功', res1);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t})\n\t\t\t\t\t\t// this.dingyue()\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t    url: '/user/tiaozhuan'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t// 支付失败回调\n\t\t\t\t\t\tconsole.error('requestPayment fail object:', err);\n\t\t\t\t\t\tconsole.error('requestPayment fail JSON:', JSON.stringify(err));\n\t\t\t\t\t\tif (err.errMsg.includes('fail cancel')) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '您已取消支付',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.error('支付失败', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付失败请检查网络',\n\t\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t\t})\n\t\t\t\t\t},\n\t\t\t\t})\n\t\t\t},\n\n\t\t\tconfirmPay() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '订单支付成功后，平台将根据订单支付时间进行服务排期。服务师傅将在48小时内跟您联系，请注意来电提醒。若您有任何问题，请及时联系客服，我们会尽力为您协调处理。',\n\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\tconfirmText: '确认',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 获取当前平台\n\t\t\t\t\t\t\tconst platform = this.getCurrentPlatform();\n\t\t\t\t\t\t\tconsole.log('当前平台:', platform);\n\n\t\t\t\t\t\t\tthis.$api.service.huodongPay({\n\t\t\t\t\t\t\t\torderId: this.id,\n\t\t\t\t\t\t\t\ttype: 1,\n\t\t\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\t\t\tif(res.code===\"-1\"){\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t\t\t\tlet obj = res.data\n\t\t\t\t\t\t\t\t\tlet packageStr = \"prepay_id=\" + obj.prepayId;\n\t\t\t\t\t\t\t\t\tconsole.log(String(packageStr))\n\t\t\t\t\t\t\t\t\tconsole.log(obj.nonceStr)\n\t\t\t\t\t\t\t\t\tconsole.log(packageStr)\n\t\t\t\t\t\t\t\t\tconsole.log(obj.nonceStr)\n\t\t\t\t\t\t\t\t\tconsole.log(String(obj.timestamp))\n\t\t\t\t\t\t\t\t\tconsole.log(obj.sign)\n\n\t\t\t\t\t\t\t\t\t// 根据平台选择不同的支付方式\n\t\t\t\t\t\t\t\t\tif (platform === 'app-plus') {\n\t\t\t\t\t\t\t\t\t\t// APP环境使用微信支付\n\t\t\t\t\t\t\t\t\t\tconsole.log('APP环境，使用微信支付');\n\t\t\t\t\t\t\t\t\t\tthis.handleAppWechatPay(obj);\n\t\t\t\t\t\t\t\t\t} else if (platform === 'mp-weixin') {\n\t\t\t\t\t\t\t\t\t\t// 微信小程序环境保持原有逻辑\n\t\t\t\t\t\t\t\t\t\tconsole.log('微信小程序环境，使用小程序支付');\n\t\t\t\t\t\t\t\t\t\tthis.handleMiniProgramPay(obj);\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t// 其他环境（H5等）\n\t\t\t\t\t\t\t\t\t\tconsole.log('其他环境，使用默认支付方式');\n\t\t\t\t\t\t\t\t\t\tthis.handleMiniProgramPay(obj);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tdingyue() {\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\tprovider: 'weixin',\n\t\t\t\t\ttmplIds: this.tmplIds,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconst anyAccepted = this.tmplIds.some(id => res[id] === 'accept');\n\t\t\t\t\t\tif (anyAccepted) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '消息订阅成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\t\t},\n\t\tasync onLoad(options) {\n\t\t\tthis.infoyouhuij = this.$store.state.service.orderInfo\n\t\t\tconsole.log(this.infoyouhuij)\n\t\t\tconsole.log(options)\n\t\t\tthis.id = options.id\n\t\t\tthis.price = options.price\n\t\t\tthis.type = options.type\n\t\t\tthis.goodsId = options.goodsId\n\t\t}\n\t}\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\tbackground-color: #f8f8f8;\n\t\theight: 100vh;\n\t\tpadding-top: 40rpx;\n\n\t\t.time {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #333333;\n\n\t\t\ttext {\n\t\t\t\tmargin-right: 15rpx;\n\t\t\t}\n\t\t}\n\n\t\t.price {\n\t\t\tdisplay: inline-block;\n\t\t\twidth: 750rpx;\n\t\t\ttext-align: center;\n\t\t\tmargin-top: 20rpx;\n\t\t\tfont-size: 80rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #292C39;\n\n\t\t\tspan {\n\t\t\t\tfont-size: 36rpx;\n\t\t\t}\n\t\t}\n\n\t\t.payCard {\n\t\t\tmargin: 0 auto;\n\t\t\twidth: 686rpx;\n\t\t\theight: 130rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder-radius: 16rpx 16rpx 16rpx 16rpx;\n\t\t\tmargin-top: 40rpx;\n\t\t\tpadding: 0 20rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\n\t\t\t.left {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\n\t\t\t\timage {\n\t\t\t\t\twidth: 70rpx;\n\t\t\t\t\theight: 70rpx;\n\t\t\t\t}\n\n\t\t\t\ttext {\n\t\t\t\t\tmargin-left: 20rpx;\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #171717;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.right {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\n\t\t\t\ttext {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #E72427;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.choose {\n\t\t\t\twidth: 40rpx;\n\t\t\t\theight: 40rpx;\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tborder: 2rpx solid #ADADAD;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t}\n\t\t}\n\n\t\t.footer {\n\t\t\twidth: 750rpx;\n\t\t\theight: 192rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tposition: fixed;\n\t\t\tbottom: 0;\n\n\t\t\t.btn {\n\t\t\t\twidth: 686rpx;\n\t\t\t\theight: 88rpx;\n\t\t\t\tbackground: #2E80FE;\n\t\t\t\tborder-radius: 44rpx 44rpx 44rpx 44rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\tline-height: 88rpx;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t}\n\t} \n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodongCashier.vue?vue&type=style&index=0&id=ba180efa&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodongCashier.vue?vue&type=style&index=0&id=ba180efa&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755661603058\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}