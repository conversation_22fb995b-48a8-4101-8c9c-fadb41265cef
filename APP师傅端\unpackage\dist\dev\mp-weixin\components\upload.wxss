@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.item-child {
  width: 216rpx;
  height: 216rpx;
  background: #f7f7f7;
  margin-bottom: 20rpx;
}
.margin {
  margin: 0 21rpx 21rpx 0;
}
.item-child:nth-child(3n) {
  margin-right: 0rpx;
}
.item-child.sm {
  width: 140rpx;
  height: 140rpx;
}
.item-child.mini {
  width: 196rpx;
  height: 196rpx;
}
.item-child.md {
  width: 335rpx;
  height: 210rpx;
  margin: 0rpx;
}
.item-child.lg {
  width: 686rpx;
  height: 400rpx;
}
.upload-img,
.upload-video {
  width: 100%;
  height: 100%;
}
.upload-video .item-delete {
  width: 60rpx;
  height: 32rpx;
  top: 0;
  right: 0;
  z-index: 2;
}
.upload-item .upload-icon {
  width: 80rpx;
  height: 76rpx;
  background: #ffffff;
}
.upload-item .upload-icon .iconfont {
  font-size: 40rpx;
  display: block;
}
.upload-item .cur-imgsize {
  line-height: 1.1;
}
.upload-item.margin {
  margin-bottom: 0;
}
.guanbi {
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0 15rpx 0 0;
  top: 0rpx;
  right: 0rpx;
  z-index: 1;
}
.guanbi .iconfont {
  font-size: 28rpx;
}
.guanbi.lg {
  width: 50rpx;
  height: 50rpx;
}
.guanbi.lg .iconfont {
  font-size: 38rpx;
}

