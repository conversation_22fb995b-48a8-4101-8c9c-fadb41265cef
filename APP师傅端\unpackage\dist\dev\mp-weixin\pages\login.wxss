@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.login-container.data-v-18804380 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}
.bg-decoration.data-v-18804380 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}
.bg-decoration .circle.data-v-18804380 {
  position: absolute;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.08);
  -webkit-animation: float-data-v-18804380 8s ease-in-out infinite;
          animation: float-data-v-18804380 8s ease-in-out infinite;
}
.bg-decoration .circle.circle-1.data-v-18804380 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  -webkit-animation-delay: 0s;
          animation-delay: 0s;
}
.bg-decoration .circle.circle-2.data-v-18804380 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  -webkit-animation-delay: 3s;
          animation-delay: 3s;
}
.bg-decoration .circle.circle-3.data-v-18804380 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 50%;
  -webkit-animation-delay: 6s;
          animation-delay: 6s;
}
@-webkit-keyframes float-data-v-18804380 {
0%,
  100% {
    -webkit-transform: translateY(0px) rotate(0deg);
            transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
}
50% {
    -webkit-transform: translateY(-15px) rotate(180deg);
            transform: translateY(-15px) rotate(180deg);
    opacity: 0.3;
}
}
@keyframes float-data-v-18804380 {
0%,
  100% {
    -webkit-transform: translateY(0px) rotate(0deg);
            transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
}
50% {
    -webkit-transform: translateY(-15px) rotate(180deg);
            transform: translateY(-15px) rotate(180deg);
    opacity: 0.3;
}
}
.header.data-v-18804380 {
  padding: 80rpx 40rpx 40rpx;
  position: relative;
  z-index: 10;
}
.header .back-btn.data-v-18804380 {
  width: 60rpx;
  height: 60rpx;
  background: rgba(59, 130, 246, 0.1);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(59, 130, 246, 0.2);
}
.header .back-btn.data-v-18804380:active {
  background: rgba(59, 130, 246, 0.15);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.logo-section.data-v-18804380 {
  text-align: center;
  padding: 0 40rpx;
  margin-bottom: 60rpx;
  position: relative;
  z-index: 10;
}
.logo-section .logo-wrapper.data-v-18804380 {
  position: relative;
  display: inline-block;
  margin-bottom: 32rpx;
}
.logo-section .logo-wrapper.data-v-18804380::before {
  content: '';
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.05));
  border-radius: 50%;
  -webkit-animation: pulse-data-v-18804380 3s ease-in-out infinite;
          animation: pulse-data-v-18804380 3s ease-in-out infinite;
}
.logo-section .logo-wrapper .logo.data-v-18804380 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  position: relative;
  z-index: 1;
  box-shadow: 0 8rpx 25rpx rgba(59, 130, 246, 0.15);
}
.logo-section .app-name.data-v-18804380 {
  font-size: 52rpx;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 16rpx;
  text-shadow: none;
}
.logo-section .welcome-text.data-v-18804380 {
  font-size: 30rpx;
  color: #64748b;
  line-height: 1.5;
  font-weight: 400;
}
@-webkit-keyframes pulse-data-v-18804380 {
0%,
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 0.8;
}
50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
    opacity: 0.4;
}
}
@keyframes pulse-data-v-18804380 {
0%,
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 0.8;
}
50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
    opacity: 0.4;
}
}
.main-card.data-v-18804380 {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-radius: 32rpx 32rpx 0 0;
  margin: 0 20rpx;
  padding: 60rpx 40rpx 40rpx;
  box-shadow: 0 -8rpx 32rpx rgba(59, 130, 246, 0.08);
  position: relative;
  z-index: 10;
  min-height: calc(100vh - 400rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}
.main-card.data-v-18804380::before {
  content: '';
  position: absolute;
  top: 20rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: #cbd5e1;
  border-radius: 3rpx;
}
.main-card .mode-title.data-v-18804380 {
  text-align: center;
  font-size: 48rpx;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 60rpx;
  position: relative;
}
.main-card .mode-title.data-v-18804380::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2rpx;
}
.main-card .form-content.data-v-18804380 {
  margin-top: 40rpx;
}
.tab-switcher.data-v-18804380 {
  display: flex;
  background: #f1f5f9;
  border-radius: 16rpx;
  margin-bottom: 50rpx;
  padding: 6rpx;
  position: relative;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}
.tab-switcher .tab-item.data-v-18804380 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 16rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #64748b;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  cursor: pointer;
}
.tab-switcher .tab-item text.data-v-18804380 {
  margin-left: 8rpx;
}
.tab-switcher .tab-item.active.data-v-18804380 {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: #fff;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.25);
  -webkit-transform: translateY(-1rpx);
          transform: translateY(-1rpx);
}
.tab-switcher .tab-item.active text.data-v-18804380 {
  color: #fff;
}
.tab-switcher .tab-item.data-v-18804380:not(.active):active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.input-group .input-item.data-v-18804380 {
  display: flex;
  align-items: center;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 20rpx;
  margin-bottom: 32rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.input-group .input-item.data-v-18804380:focus-within {
  border-color: #3b82f6;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);
}
.input-group .input-item.optional .input-label.data-v-18804380::after {
  content: ' (可获得奖励)';
  color: #f59e0b;
  font-size: 22rpx;
}
.input-group .input-item .input-icon.data-v-18804380 {
  padding: 32rpx 24rpx 32rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.input-group .input-item .input-content.data-v-18804380 {
  flex: 1;
  padding: 16rpx 0;
}
.input-group .input-item .input-content .input-label.data-v-18804380 {
  font-size: 24rpx;
  color: #64748b;
  margin-bottom: 8rpx;
  font-weight: 500;
}
.input-group .input-item .input-content .input-label .optional-tag.data-v-18804380 {
  color: #f59e0b;
  font-size: 20rpx;
  margin-left: 8rpx;
  background: rgba(245, 158, 11, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}
.input-group .input-item .input-content .input-field.data-v-18804380 {
  width: 100%;
  font-size: 32rpx;
  color: #1e293b;
  background: transparent;
  border: none;
  outline: none;
}
.input-group .input-item .input-content .input-field.data-v-18804380::-webkit-input-placeholder {
  color: #94a3b8;
  font-size: 30rpx;
}
.input-group .input-item .input-content .input-field.data-v-18804380::placeholder {
  color: #94a3b8;
  font-size: 30rpx;
}
.input-group .input-item .input-field.data-v-18804380 {
  flex: 1;
  padding: 32rpx 0;
  font-size: 32rpx;
  color: #1e293b;
  background: transparent;
  border: none;
  outline: none;
}
.input-group .input-item .input-field.data-v-18804380::-webkit-input-placeholder {
  color: #94a3b8;
  font-size: 30rpx;
}
.input-group .input-item .input-field.data-v-18804380::placeholder {
  color: #94a3b8;
  font-size: 30rpx;
}
.input-group .input-item .action-icon.data-v-18804380 {
  padding: 32rpx 32rpx 32rpx 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}
.input-group .input-item .action-icon.data-v-18804380:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.input-group .input-item .input-status.data-v-18804380 {
  padding: 32rpx 32rpx 32rpx 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.input-group .input-item .input-benefit.data-v-18804380 {
  padding: 32rpx 32rpx 32rpx 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.input-group .input-item .sms-btn.data-v-18804380 {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: #fff;
  padding: 20rpx 24rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  margin-right: 16rpx;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  white-space: nowrap;
}
.input-group .input-item .sms-btn.data-v-18804380:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.input-group .input-item .sms-btn.disabled.data-v-18804380 {
  background: #94a3b8;
  cursor: not-allowed;
  -webkit-transform: none;
          transform: none;
}
.input-group .input-item .sms-btn.disabled.data-v-18804380:active {
  -webkit-transform: none;
          transform: none;
}
.password-strength.data-v-18804380 {
  display: flex;
  align-items: center;
  margin: -16rpx 0 32rpx 0;
  padding: 0 32rpx;
}
.password-strength .strength-label.data-v-18804380 {
  font-size: 24rpx;
  color: #64748b;
  margin-right: 16rpx;
}
.password-strength .strength-bar.data-v-18804380 {
  display: flex;
  gap: 8rpx;
  margin-right: 16rpx;
}
.password-strength .strength-bar .strength-item.data-v-18804380 {
  width: 40rpx;
  height: 6rpx;
  background: #e2e8f0;
  border-radius: 3rpx;
  transition: all 0.3s ease;
}
.password-strength .strength-bar .strength-item.active.data-v-18804380:nth-child(1) {
  background: #ef4444;
}
.password-strength .strength-bar .strength-item.active.data-v-18804380:nth-child(2) {
  background: #f59e0b;
}
.password-strength .strength-bar .strength-item.active.data-v-18804380:nth-child(3) {
  background: #10b981;
}
.password-strength .strength-text.data-v-18804380 {
  font-size: 24rpx;
  font-weight: 600;
}
.password-strength .strength-text.data-v-18804380:nth-of-type(1) {
  color: #ef4444;
}
.password-strength .strength-text.data-v-18804380:nth-of-type(2) {
  color: #f59e0b;
}
.password-strength .strength-text.data-v-18804380:nth-of-type(3) {
  color: #10b981;
}
.login-links.data-v-18804380 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32rpx;
}
.login-links .forgot-link.data-v-18804380 {
  color: #3b82f6;
  font-size: 28rpx;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s ease;
}
.login-links .forgot-link.data-v-18804380:active {
  color: #1d4ed8;
}
.login-links .register-link.data-v-18804380 {
  color: #64748b;
  font-size: 28rpx;
}
.login-links .register-link .link-highlight.data-v-18804380 {
  color: #3b82f6;
  font-weight: 600;
  cursor: pointer;
  transition: color 0.2s ease;
}
.login-links .register-link .link-highlight.data-v-18804380:active {
  color: #1d4ed8;
}
.agreement-section.data-v-18804380 {
  margin: 40rpx 0;
}
.agreement-section .checkbox-container.data-v-18804380 {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
}
.agreement-section .checkbox-container .checkbox.data-v-18804380 {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #cbd5e1;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
  transition: all 0.3s ease;
  background: #fff;
}
.agreement-section .checkbox-container .checkbox.checked.data-v-18804380 {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-color: #3b82f6;
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.agreement-section .checkbox-container .checkbox.data-v-18804380:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.agreement-section .checkbox-container .agreement-text.data-v-18804380 {
  font-size: 26rpx;
  color: #64748b;
  line-height: 1.6;
  flex: 1;
}
.agreement-section .checkbox-container .agreement-text .link.data-v-18804380 {
  color: #3b82f6;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s ease;
}
.agreement-section .checkbox-container .agreement-text .link.data-v-18804380:active {
  color: #1d4ed8;
}
.action-button.data-v-18804380 {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 36rpx;
  font-weight: 700;
  margin: 40rpx 0 32rpx;
  box-shadow: 0 8rpx 25rpx rgba(59, 130, 246, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.action-button.data-v-18804380::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}
.action-button.data-v-18804380:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(59, 130, 246, 0.4);
}
.action-button.data-v-18804380:active::before {
  left: 100%;
}
.action-button.disabled.data-v-18804380 {
  background: #94a3b8;
  box-shadow: none;
  cursor: not-allowed;
  -webkit-transform: none;
          transform: none;
}
.action-button.disabled.data-v-18804380:active {
  -webkit-transform: none;
          transform: none;
}
.action-button.disabled.data-v-18804380::before {
  display: none;
}
.action-button .loading-icon.data-v-18804380 {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #fff;
  border-radius: 50%;
  -webkit-animation: spin-data-v-18804380 1s linear infinite;
          animation: spin-data-v-18804380 1s linear infinite;
  margin-right: 16rpx;
}
@-webkit-keyframes spin-data-v-18804380 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-18804380 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.wechat-login-button.data-v-18804380 {
  width: 100%;
  height: 100rpx;
  background: #07c160;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 36rpx;
  font-weight: 700;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 25rpx rgba(7, 193, 96, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.wechat-login-button.data-v-18804380::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}
.wechat-login-button.data-v-18804380:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(7, 193, 96, 0.4);
}
.wechat-login-button.data-v-18804380:active::before {
  left: 100%;
}
.wechat-login-button.disabled.data-v-18804380 {
  background: #94a3b8;
  box-shadow: none;
  cursor: not-allowed;
  -webkit-transform: none;
          transform: none;
}
.wechat-login-button.disabled.data-v-18804380:active {
  -webkit-transform: none;
          transform: none;
}
.wechat-login-button.disabled.data-v-18804380::before {
  display: none;
}
.wechat-login-button .loading-icon.data-v-18804380 {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #fff;
  border-radius: 50%;
  -webkit-animation: spin-data-v-18804380 1s linear infinite;
          animation: spin-data-v-18804380 1s linear infinite;
  margin-right: 16rpx;
}
.wechat-login-button .wechat-icon.data-v-18804380 {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTI4LjMzMzMgMTMuMzMzM0MyOC4zMzMzIDEwLjM4MSAyNS45NTI0IDggMjMgOEgxN0MxNC4wNDc2IDggMTEuNjY2NyAxMC4zODEgMTEuNjY2NyAxMy4zMzMzVjE4LjY2NjdDMTEuNjY2NyAyMS42MTkgMTQuMDQ3NiAyNCAxNyAyNEgyM0MyNS45NTI0IDI0IDI4LjMzMzMgMjEuNjE5IDI4LjMzMzMgMTguNjY2N1YxMy4zMzMzWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg==") no-repeat center;
  background-size: contain;
}
.switch-links.data-v-18804380 {
  text-align: center;
  margin-top: 20rpx;
}
.switch-links .link-text.data-v-18804380 {
  color: #64748b;
  font-size: 28rpx;
  line-height: 1.5;
}
.switch-links .link-text .link-highlight.data-v-18804380 {
  color: #3b82f6;
  font-weight: 600;
  cursor: pointer;
  transition: color 0.2s ease;
}
.switch-links .link-text .link-highlight.data-v-18804380:active {
  color: #1d4ed8;
}
.bottom-decoration.data-v-18804380 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  pointer-events: none;
}
.bottom-decoration .wave.data-v-18804380 {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
  -webkit-clip-path: polygon(0 50%, 100% 80%, 100% 100%, 0% 100%);
          clip-path: polygon(0 50%, 100% 80%, 100% 100%, 0% 100%);
}
@media screen and (max-width: 750rpx) {
.main-card.data-v-18804380 {
    margin: 0 10rpx;
    padding: 50rpx 30rpx 30rpx;
}
.logo-section.data-v-18804380 {
    padding: 0 30rpx;
    margin-bottom: 50rpx;
}
.logo-section .app-name.data-v-18804380 {
    font-size: 48rpx;
}
.logo-section .welcome-text.data-v-18804380 {
    font-size: 28rpx;
}
.input-group .input-item.data-v-18804380 {
    margin-bottom: 28rpx;
}
.input-group .input-item .input-field.data-v-18804380 {
    font-size: 30rpx;
}
.input-group .input-item .sms-btn.data-v-18804380 {
    font-size: 24rpx;
    padding: 18rpx 20rpx;
}
.action-button.data-v-18804380,
  .wechat-login-button.data-v-18804380 {
    height: 96rpx;
    font-size: 34rpx;
}
}

