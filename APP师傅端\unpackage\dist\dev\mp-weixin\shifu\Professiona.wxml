<view class="container data-v-47061b1b"><block wx:for="{{menuItems}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleItemClick',['$0'],[[['menuItems','',index]]]]]]]}}" class="menu-item data-v-47061b1b" bindtap="__e"><text class="item-text data-v-47061b1b">{{item.name}}</text><view class="image-status data-v-47061b1b"><block wx:if="{{form[item.imgField]}}"><text class="uploaded data-v-47061b1b">已上传</text></block><text class="arrow data-v-47061b1b">></text></view></view></block><u-modal vue-id="cc846d6a-1" show="{{showModal}}" title="{{'开通'+selectedItem.name}}" showCancelButton="{{true}}" confirmText="确定" cancelText="取消" data-event-opts="{{[['^confirm',[['confirmUpload']]],['^cancel',[['closeModal']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-47061b1b" bind:__l="__l" vue-slots="{{['default']}}"><view class="modal-content data-v-47061b1b"><view class="upload-box data-v-47061b1b"><block wx:if="{{!$root.m0}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="upload-button data-v-47061b1b" bindtap="__e"><view class="upload-icon data-v-47061b1b"><text class="icon data-v-47061b1b">+</text></view><text class="upload-text data-v-47061b1b">上传证件图片</text></view></block><block wx:if="{{$root.m1}}"><view class="image-preview data-v-47061b1b"><image style="width:200px;height:200px;" src="{{$root.m2}}" mode="aspectFit" class="data-v-47061b1b"></image><view data-event-opts="{{[['tap',[['deleteImage',['$event']]]]]}}" class="delete-button data-v-47061b1b" bindtap="__e">删除</view></view></block></view></view></u-modal></view>