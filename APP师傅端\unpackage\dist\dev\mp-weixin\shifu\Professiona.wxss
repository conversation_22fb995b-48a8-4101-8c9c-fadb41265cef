@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.header.data-v-47061b1b {
  width: 750rpx;
  height: 58rpx;
  background: #fff7f1;
  line-height: 58rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 400;
}
.container.data-v-47061b1b {
  background-color: #f7f9fc;
  min-height: 100vh;
  padding: 10px;
}
.menu-item.data-v-47061b1b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #ffffff;
  border-radius: 8px;
  margin-bottom: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.item-text.data-v-47061b1b {
  font-size: 16px;
  color: #2c3e50;
  font-weight: 500;
}
.image-status.data-v-47061b1b {
  display: flex;
  align-items: center;
}
.uploaded.data-v-47061b1b {
  color: #28a745;
  font-size: 14px;
  margin-right: 8px;
}
.arrow.data-v-47061b1b {
  color: #7f8c8d;
  font-size: 16px;
}
.modal-content.data-v-47061b1b {
  padding: 20px;
  text-align: center;
}
.upload-box.data-v-47061b1b {
  margin: 15px 0;
}
.upload-button.data-v-47061b1b {
  width: 200px;
  height: 200px;
  border: 1px dashed #cccccc;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  cursor: pointer;
}
.upload-icon.data-v-47061b1b {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: #f2f2f2;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}
.icon.data-v-47061b1b {
  font-size: 36px;
  color: #999999;
}
.upload-text.data-v-47061b1b {
  font-size: 14px;
  color: #666666;
}
.image-preview.data-v-47061b1b {
  position: relative;
  width: 200px;
  margin: 0 auto;
}
.delete-button.data-v-47061b1b {
  position: absolute;
  bottom: -30px;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  background-color: #ff4d4f;
  color: white;
  padding: 5px 15px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

