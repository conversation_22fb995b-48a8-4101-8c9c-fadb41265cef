@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-7800824c {
  background: #f8f8f8;
  height: 100vh;
  padding: 40rpx 30rpx;
}
.page .header.data-v-7800824c {
  text-align: center;
  font-size: 52rpx;
  font-weight: 600;
  color: #000000;
}
.page .box.data-v-7800824c {
  margin-top: 40rpx;
  width: 690rpx;
  height: 748rpx;
  background: #FFFFFF;
  border-radius: 32rpx;
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.page .box .name.data-v-7800824c {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  text-align: center;
}
.page .box .desc.data-v-7800824c {
  margin-top: 20rpx;
  font-size: 24rpx;
  text-align: center;
  font-weight: 400;
  color: #000000;
}
.page .box image.data-v-7800824c {
  width: 444rpx;
  height: 444rpx;
  margin: 18rpx auto 0;
}
.page .box .invite-code-container.data-v-7800824c {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
  gap: 20rpx;
}
.page .box .invite-code.data-v-7800824c {
  font-size: 28rpx;
  font-weight: 400;
  color: #000000;
}
.page .box .copy-btn.data-v-7800824c {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background: #2E80FE;
  color: #FFFFFF;
  font-size: 24rpx;
  border-radius: 30rpx;
}
.page .button-container.data-v-7800824c {
  display: flex;
  gap: 20rpx;
  position: absolute;
  bottom: 42rpx;
  width: 690rpx;
}
.page .btn.data-v-7800824c {
  flex: 1;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 50rpx;
  line-height: 98rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
}
.page .save-btn.data-v-7800824c {
  /* Specific styles for save button if needed */
}
.page .share-btn.data-v-7800824c {
  /* Ensure button inherits same styles */
  border: none;
  padding: 0;
  margin: 0;
  background: #2E80FE;
  /* Remove default button styles */
}
.page .share-btn.data-v-7800824c:after {
  border: none;
}
.page .share-btn[disabled].data-v-7800824c {
  background: #cccccc;
  pointer-events: none;
}

