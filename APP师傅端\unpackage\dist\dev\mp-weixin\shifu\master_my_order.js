(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["shifu/master_my_order"],{

/***/ 587:
/*!*******************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/main.js?{"page":"shifu%2Fmaster_my_order"} ***!
  \*******************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/uni-stat/dist/uni-stat.es.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _master_my_order = _interopRequireDefault(__webpack_require__(/*! ./shifu/master_my_order.vue */ 588));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_master_my_order.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 588:
/*!**************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue ***!
  \**************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _master_my_order_vue_vue_type_template_id_b530dfe4_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./master_my_order.vue?vue&type=template&id=b530dfe4&scoped=true& */ 589);
/* harmony import */ var _master_my_order_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./master_my_order.vue?vue&type=script&lang=js& */ 591);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _master_my_order_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _master_my_order_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _master_my_order_vue_vue_type_style_index_0_id_b530dfe4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./master_my_order.vue?vue&type=style&index=0&id=b530dfe4&scoped=true&lang=scss& */ 593);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 67);

var renderjs





/* normalize component */

var component = Object(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _master_my_order_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _master_my_order_vue_vue_type_template_id_b530dfe4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _master_my_order_vue_vue_type_template_id_b530dfe4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "b530dfe4",
  null,
  false,
  _master_my_order_vue_vue_type_template_id_b530dfe4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "shifu/master_my_order.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 589:
/*!*********************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?vue&type=template&id=b530dfe4&scoped=true& ***!
  \*********************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_template_id_b530dfe4_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./master_my_order.vue?vue&type=template&id=b530dfe4&scoped=true& */ 590);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_template_id_b530dfe4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_template_id_b530dfe4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_template_id_b530dfe4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_template_id_b530dfe4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 590:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?vue&type=template&id=b530dfe4&scoped=true& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uLoadmore: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-loadmore/u-loadmore */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-loadmore/u-loadmore")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-loadmore/u-loadmore.vue */ 803))
    },
    "u-Form": function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u--form/u--form */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u--form/u--form")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u--form/u--form.vue */ 976))
    },
    uFormItem: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-form-item/u-form-item */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-form-item/u-form-item")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-form-item/u-form-item.vue */ 982))
    },
    "u-Input": function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u--input/u--input */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u--input/u--input")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u--input/u--input.vue */ 946))
    },
    "u-Textarea": function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u--textarea/u--textarea */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u--textarea/u--textarea")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u--textarea/u--textarea.vue */ 990))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l1 = _vm.__map(_vm.orderList, function (item, index) {
    var $orig = _vm.__get_orig(item)
    var g0 = item.orderDiffPriceList && item.orderDiffPriceList.length > 0
    var m0 = g0 ? _vm.getScrollViewHeight(item.orderDiffPriceList.length) : null
    var l0 = g0
      ? _vm.__map(item.orderDiffPriceList, function (diffItem, diffIndex) {
          var $orig = _vm.__get_orig(diffItem)
          var m1 = _vm.getDiffStatusText(diffItem.status)
          var m2 = _vm.formatWarrantyDate(diffItem.partsWarrantyPeriod)
          return {
            $orig: $orig,
            m1: m1,
            m2: m2,
          }
        })
      : null
    return {
      $orig: $orig,
      g0: g0,
      m0: m0,
      l0: l0,
    }
  })
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l1: l1,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 591:
/*!***************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./master_my_order.vue?vue&type=script&lang=js& */ 592);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 592:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 36));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 38));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var Upload = function Upload() {
  Promise.all(/*! require.ensure | components/upload */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/upload")]).then((function () {
    return resolve(__webpack_require__(/*! @/components/upload.vue */ 755));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
// Import upload component
var _default = {
  components: {
    Upload: Upload
  },
  data: function data() {
    return {
      limit: 10,
      coachId: '',
      shifuId: '',
      tmplIds: [' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo', '9sT8DPghuzkjRmg3gBefKWgrZHMIkJs0l7hZKgL5SWY', 'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs', 'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'],
      status: 'loadmore',
      list: [{
        name: '全部',
        value: 0
      }, {
        name: '待上门',
        value: 3
      }, {
        name: '待服务',
        value: 5
      }, {
        name: '服务中',
        value: 6
      }, {
        name: '已完成',
        value: 7
      }, {
        name: '售后',
        value: 8
      }],
      currentIndex: 0,
      page: 0,
      orderList: [],
      pay_typeArr: ['', '待支付', '已支付', '已接单', '上门中', '待服务', '服务中', '已完成', '售后'],
      isLoading: false,
      // Flag to prevent multiple API calls

      // For diffapply modal
      showDiffApply: false,
      currentOrderItemForDiff: null,
      // To store the item for which diff apply is initiated
      diffApplyForm: {
        diffAmount: '',
        reasonType: 1,
        // 差价原因类型，目前固定为1代表配件不符合
        reasonDetail: '',
        partsimgs: [] // 配件图片
      },

      diffApplyRules: {
        diffAmount: [{
          required: true,
          message: '请输入差价金额',
          trigger: ['blur', 'change']
        }, {
          validator: function validator(rule, value, callback) {
            return value >= 0.01;
          },
          message: '差价金额必须大于等于0.01',
          trigger: ['blur', 'change']
        }],
        reasonDetail: [{
          required: true,
          message: '请输入差价原因详情',
          trigger: ['blur', 'change']
        }]
      }
    };
  },
  onReady: function onReady() {
    // 由于弹窗默认隐藏，表单引用可能不存在，所以在显示弹窗时再设置规则
  },
  onReachBottom: function onReachBottom() {
    this.loadMore();
  },
  onPullDownRefresh: function onPullDownRefresh() {
    this.refreshList();
  },
  methods: {
    // 获取差价申请状态文本
    getDiffStatusText: function getDiffStatusText(status) {
      var statusMap = {
        '-1': '已取消',
        0: '待确认',
        1: '已确认待支付',
        2: '已支付',
        3: '已拒绝'
      };
      return statusMap[status] || '未知状态';
    },
    // 格式化配件质保日期
    formatWarrantyDate: function formatWarrantyDate(timestamp) {
      console.log('formatWarrantyDate 输入参数:', timestamp, '类型:', (0, _typeof2.default)(timestamp));
      if (!timestamp) {
        console.log('timestamp 为空，返回无质保信息');
        return '无质保信息';
      }
      try {
        var date = new Date(timestamp);
        console.log('创建的日期对象:', date);
        if (isNaN(date.getTime())) {
          console.log('日期无效，返回无效日期');
          return '无效日期';
        }
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        var result = "".concat(year, "-").concat(month, "-").concat(day);
        console.log('格式化结果:', result);
        return result;
      } catch (error) {
        console.error('格式化质保日期失败:', error);
        return '格式错误';
      }
    },
    // 获取滚动视图高度
    getScrollViewHeight: function getScrollViewHeight(itemCount) {
      // 每个项目大约150rpx高度，最多显示3个项目，超过则可滚动
      var maxHeight = 450; // 3 * 150rpx
      var itemHeight = 150;
      var calculatedHeight = Math.min(itemCount * itemHeight, maxHeight);
      return calculatedHeight;
    },
    showDiffApplyModal: function showDiffApplyModal(item) {
      var _this = this;
      this.currentOrderItemForDiff = item;
      this.diffApplyForm = {
        // Reset form for new application
        diffAmount: '',
        reasonType: 1,
        // 差价原因类型，目前固定为1代表配件不符合
        reasonDetail: '',
        partsimgs: [] // 配件图片
      };

      this.showDiffApply = true;

      // 在下一个tick中设置表单规则，确保DOM已渲染
      this.$nextTick(function () {
        if (_this.$refs.diffApplyForm && _this.$refs.diffApplyForm.setRules) {
          _this.$refs.diffApplyForm.setRules(_this.diffApplyRules);
        }
      });
    },
    // 处理差价申请中的图片上传
    imgUploadDiff: function imgUploadDiff(e) {
      console.log('imgUploadDiff event:', e);
      var imagelist = e.imagelist,
        imgtype = e.imgtype;
      this.$set(this.diffApplyForm, imgtype, imagelist);
    },
    // 关闭差价申请弹窗
    closeDiffApplyModal: function closeDiffApplyModal() {
      this.showDiffApply = false;
      // 重置表单数据
      this.diffApplyForm = {
        diffAmount: '',
        reasonType: 1,
        reasonDetail: '',
        partsimgs: []
      };
    },
    diffApplyConfirm: function diffApplyConfirm() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var partsImgsString, res;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                if (!(!_this2.$refs.diffApplyForm || !_this2.$refs.diffApplyForm.validate)) {
                  _context.next = 4;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: '表单未准备就绪，请稍后重试'
                });
                return _context.abrupt("return");
              case 4:
                _context.next = 6;
                return _this2.$refs.diffApplyForm.validate();
              case 6:
                if (_this2.currentOrderItemForDiff) {
                  _context.next = 9;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: '订单信息缺失'
                });
                return _context.abrupt("return");
              case 9:
                _context.prev = 9;
                // 准备配件图片数据 - 根据API文档，partsImgs是string类型，需要转换
                partsImgsString = _this2.diffApplyForm.partsimgs.map(function (img) {
                  return img.path;
                }).join(',');
                _context.next = 13;
                return _this2.$api.shifu.diffApply({
                  orderId: _this2.currentOrderItemForDiff.id,
                  diffAmount: parseFloat(_this2.diffApplyForm.diffAmount),
                  reasonType: _this2.diffApplyForm.reasonType,
                  // 差价原因类型(1配件不符合)
                  reasonDetail: _this2.diffApplyForm.reasonDetail,
                  partsImgs: partsImgsString // 配件图片，string类型，多个图片用逗号分隔
                });
              case 13:
                res = _context.sent;
                if (res.code === "200") {
                  uni.showToast({
                    title: '差价申请成功',
                    icon: 'success'
                  });
                  _this2.showDiffApply = false;
                  _this2.refreshList(); // Refresh list to reflect changes
                } else {
                  uni.showToast({
                    title: res.msg || '差价申请失败',
                    icon: 'none'
                  });
                }
                _context.next = 21;
                break;
              case 17:
                _context.prev = 17;
                _context.t0 = _context["catch"](9);
                uni.showToast({
                  title: '请求失败',
                  icon: 'none'
                });
                console.error('Error in diffApply:', _context.t0);
              case 21:
                _context.next = 27;
                break;
              case 23:
                _context.prev = 23;
                _context.t1 = _context["catch"](0);
                console.error('Form validation failed:', _context.t1);
                uni.showToast({
                  icon: 'none',
                  title: '请检查填写信息'
                });
              case 27:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[0, 23], [9, 17]]);
      }))();
    },
    showDiffCancelModal: function showDiffCancelModal(item, diffItem) {
      var _this3 = this;
      var title = diffItem ? '取消差价申请' : '取消差价申请';
      var content = diffItem ? "\u786E\u5B9A\u8981\u53D6\u6D88\u5DEE\u4EF7\u5355\u53F7 ".concat(diffItem.diffCode, " \u7684\u7533\u8BF7\u5417\uFF1F") : '确定要取消此订单的差价申请吗？';
      uni.showModal({
        title: title,
        content: content,
        confirmText: '确定',
        cancelText: '取消',
        success: function success(res) {
          if (res.confirm) {
            _this3.diffCancel(item, diffItem);
          }
        }
      });
    },
    diffCancel: function diffCancel(item, diffItem) {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var cancelId, res;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                console.log(diffItem);
                // 如果有具体的差价申请项，使用差价申请的ID，否则使用订单ID
                cancelId = diffItem.id;
                _context2.next = 5;
                return _this4.$api.shifu.diffCancel({
                  id: cancelId
                });
              case 5:
                res = _context2.sent;
                if (res.code === "200") {
                  uni.showToast({
                    title: '差价申请已取消',
                    icon: 'success'
                  });
                  _this4.refreshList();
                } else {
                  uni.showToast({
                    title: res.msg || '取消失败',
                    icon: 'none'
                  });
                }
                _context2.next = 13;
                break;
              case 9:
                _context2.prev = 9;
                _context2.t0 = _context2["catch"](0);
                uni.showToast({
                  title: '请求失败',
                  icon: 'none'
                });
                console.error('Error in diffCancel:', _context2.t0);
              case 13:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 9]]);
      }))();
    },
    cancellModal: function cancellModal(item) {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                uni.showModal({
                  title: '取消订单',
                  content: '确定要取消此订单吗？',
                  confirmText: '确定',
                  cancelText: '取消',
                  success: function () {
                    var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3(res) {
                      var response;
                      return _regenerator.default.wrap(function _callee3$(_context3) {
                        while (1) {
                          switch (_context3.prev = _context3.next) {
                            case 0:
                              if (!res.confirm) {
                                _context3.next = 12;
                                break;
                              }
                              _context3.prev = 1;
                              _context3.next = 4;
                              return _this5.$api.shifu.orderCancel({
                                id: item.id
                              });
                            case 4:
                              response = _context3.sent;
                              if (response.code === "200") {
                                uni.showToast({
                                  icon: 'success',
                                  title: response.msg || '订单已取消'
                                });
                                _this5.getList();
                              } else {
                                uni.showToast({
                                  icon: 'none',
                                  title: response.msg || '取消失败，请重试'
                                });
                              }
                              _context3.next = 12;
                              break;
                            case 8:
                              _context3.prev = 8;
                              _context3.t0 = _context3["catch"](1);
                              console.error('Error cancelling order:', _context3.t0);
                              uni.showToast({
                                icon: 'none',
                                title: _context3.t0.message || '网络请求失败，请检查网络连接'
                              });
                            case 12:
                            case "end":
                              return _context3.stop();
                          }
                        }
                      }, _callee3, null, [[1, 8]]);
                    }));
                    function success(_x) {
                      return _success.apply(this, arguments);
                    }
                    return success;
                  }()
                });
              case 1:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4);
      }))();
    },
    dingyue: function dingyue() {
      var _this6 = this;
      console.log('dingyue called');
      var allTmplIds = this.tmplIds;
      var requiredTmplId = '9sT8DPghuzkjRmg3gBefKWgrZHMIkJs0l7hZKgL5SWY';
      if (allTmplIds.length < 3) {
        console.error("Not enough template IDs available:", allTmplIds);
        return;
      }
      // Ensure requiredTmplId is included, select 2 more randomly
      var otherTmplIds = allTmplIds.filter(function (id) {
        return id !== requiredTmplId;
      });
      var shuffled = otherTmplIds.sort(function () {
        return 0.5 - Math.random();
      });
      var selectedTmplIds = [requiredTmplId].concat((0, _toConsumableArray2.default)(shuffled.slice(0, 2)));
      console.log("Selected template IDs:", selectedTmplIds);
      var templateData = selectedTmplIds.map(function (id, index) {
        return {
          templateId: id,
          templateCategoryId: index === 0 ? 10 : 5
        };
      });
      uni.requestSubscribeMessage({
        tmplIds: selectedTmplIds,
        success: function success(res) {
          console.log('requestSubscribeMessage result:', res);
          _this6.templateCategoryIds = [];
          var count = 0;
          selectedTmplIds.forEach(function (templId, index) {
            console.log("Template ".concat(templId, " status: ").concat(res[templId]));
            if (res[templId] === 'accept') {
              var templateCategoryId = templateData[index].templateCategoryId;
              if (templateCategoryId === 10) {
                for (var i = 0; i < 15; i++) {
                  _this6.templateCategoryIds.push(templateCategoryId);
                }
              }
              // Do not push if templateCategoryId is 5
              // else {
              // 	this.templateCategoryIds.push(templateCategoryId);
              // }
              console.log('Accepted message push for template:', templId);
            }
          });
          console.log('Updated templateCategoryIds:', _this6.templateCategoryIds);
        },
        fail: function fail(err) {
          console.error('requestSubscribeMessage failed:', err);
        }
      });
    },
    loadMore: function loadMore() {
      if (this.status === 'nomore' || this.isLoading) return;
      this.isLoading = true;
      this.status = 'loading';
      var nextPage = this.page + 1;
      this.fetchOrders(nextPage, false);
    },
    refreshList: function refreshList() {
      this.page = 0;
      this.orderList = [];
      this.status = 'loadmore';
      this.getList();
      setTimeout(function () {
        uni.stopPullDownRefresh();
      }, 1000);
    },
    fetchOrders: function fetchOrders(pageNum) {
      var _this7 = this;
      var replaceList = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
      var apiParams = {
        coachId: this.shifuId,
        pageNum: pageNum,
        pageSize: this.limit
      };

      // 只有当不是"全部"时才传递payType参数
      if (this.currentIndex !== 0) {
        apiParams.payType = this.currentIndex === 8 ? 7 : this.currentIndex;
      }
      return this.$api.shifu.master_Order(apiParams).then(function (res) {
        if (res.code === '-1' || !res.data || !res.data.list) {
          uni.showToast({
            icon: 'none',
            title: res.msg || '没有更多数据'
          });
          _this7.status = 'nomore';
        } else {
          var list = Array.isArray(res.data.list) ? res.data.list : [];
          var normalizedList = list.filter(function (item) {
            return _this7.currentIndex !== 8 || item.isAftermarket === 1;
          }).map(function (item) {
            return _objectSpread(_objectSpread({}, item), {}, {
              payType: parseInt(item.payType),
              // 确保 orderDiffPriceList 存在
              orderDiffPriceList: Array.isArray(item.orderDiffPriceList) ? item.orderDiffPriceList : []
            }, item.orderDiffPriceList.length === 0 && {
              orderDiffPriceList: [{
                id: 'test-' + item.id,
                diffCode: 'TEST' + Date.now(),
                status: 0,
                diffAmount: 100,
                reasonDetail: '测试差价申请',
                createdTime: '2025-08-20 12:00:00',
                partsWarrantyPeriod: Date.now() + 365 * 24 * 60 * 60 * 1000 // 一年后的时间戳
              }]
            });
          });

          if (replaceList) {
            _this7.orderList = normalizedList;
          } else {
            _this7.orderList = [].concat((0, _toConsumableArray2.default)(_this7.orderList), (0, _toConsumableArray2.default)(normalizedList));
          }
          _this7.page = pageNum;
          _this7.status = list.length < _this7.limit ? 'nomore' : 'loadmore';
        }
        _this7.isLoading = false;
        return res;
      }).catch(function (err) {
        _this7.status = 'nomore';
        _this7.isLoading = false;
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
        console.error('Error loading data:', err);
        return Promise.reject(err);
      });
    },
    goDetail: function goDetail(item) {
      uni.setStorageSync('orderdetails', item);
      uni.navigateTo({
        url: "/shifu/master_order_my?id=".concat(item.id)
      });
    },
    goUrl: function goUrl(e) {
      uni.navigateTo({
        url: e
      });
    },
    showConfirmModal: function showConfirmModal(item, action) {
      var _this8 = this;
      uni.showModal({
        title: action === 'queren' ? '确认到达' : '开始服务',
        content: '请确认操作：' + (action === 'queren' ? '确认到达' : '开始服务'),
        confirmText: '确定',
        cancelText: '取消',
        success: function success(res) {
          if (res.confirm) {
            if (action === 'queren') {
              _this8.queren(item);
            } else if (action === 'startFu') {
              _this8.startFu(item);
            }
          }
        }
      });
    },
    startFu: function startFu(item) {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var res;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                _context5.next = 3;
                return _this9.$api.shifu.shifuqueren({
                  id: item.id,
                  payType: 6
                });
              case 3:
                res = _context5.sent;
                if (res.code === "200") {
                  uni.showToast({
                    title: '操作成功',
                    icon: 'success'
                  });
                  _this9.refreshList();
                } else {
                  uni.showToast({
                    title: res.msg || '操作失败',
                    icon: 'none'
                  });
                }
                _context5.next = 11;
                break;
              case 7:
                _context5.prev = 7;
                _context5.t0 = _context5["catch"](0);
                uni.showToast({
                  title: '请求失败',
                  icon: 'none'
                });
                console.error('Error in startFu:', _context5.t0);
              case 11:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 7]]);
      }))();
    },
    queren: function queren(item) {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var res;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                _context6.prev = 0;
                _context6.next = 3;
                return _this10.$api.shifu.shifuqueren({
                  id: item.id,
                  payType: 5
                });
              case 3:
                res = _context6.sent;
                if (res.code === "200") {
                  uni.showToast({
                    title: '操作成功',
                    icon: 'success'
                  });
                  _this10.refreshList();
                } else {
                  uni.showToast({
                    title: res.msg || '操作失败',
                    icon: 'none'
                  });
                }
                _context6.next = 11;
                break;
              case 7:
                _context6.prev = 7;
                _context6.t0 = _context6["catch"](0);
                uni.showToast({
                  title: '请求失败',
                  icon: 'none'
                });
                console.error('Error in queren:', _context6.t0);
              case 11:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[0, 7]]);
      }))();
    },
    updateHigh: function updateHigh(options) {
      var shiInfo = uni.getStorageSync('shiInfo');
      if (!shiInfo) {
        console.log('No shiInfo, skipping updateHighlight');
        return;
      }
      var shiInfoid;
      try {
        shiInfoid = JSON.parse(shiInfo);
      } catch (e) {
        console.error('Error parsing shiInfo:', e);
        return;
      }
      this.$api.service.updataHighlight({
        userId: shiInfoid.id,
        role: 2,
        payType: options.tab
      }).then(function (res) {
        console.log(res);
      }).catch(function (err) {
        console.error('Error updating highlight:', err);
      });
    },
    getList: function getList() {
      if (this.isLoading) return;
      this.isLoading = true;
      this.status = 'loading';
      this.fetchOrders(1, true);
    },
    handleHeader: function handleHeader(item) {
      if (this.currentIndex === item.value) return;
      this.currentIndex = item.value;
      this.page = 0;
      this.orderList = [];
      this.status = 'loadmore';
      this.getList();
      this.updateHigh({
        tab: item.value
      });
    }
  },
  onLoad: function onLoad(options) {
    var shiInfo = uni.getStorageSync('shiInfo') || '{}';
    try {
      this.shifuId = JSON.parse(shiInfo).id;
    } catch (e) {
      console.error('Error parsing shiInfo:', e);
      this.shifuId = '';
    }
    if (options.tab) {
      this.currentIndex = parseInt(options.tab);
    }
    this.updateHigh(options);
    this.getList();
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 593:
/*!************************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?vue&type=style&index=0&id=b530dfe4&scoped=true&lang=scss& ***!
  \************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_style_index_0_id_b530dfe4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./master_my_order.vue?vue&type=style&index=0&id=b530dfe4&scoped=true&lang=scss& */ 594);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_style_index_0_id_b530dfe4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_style_index_0_id_b530dfe4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_style_index_0_id_b530dfe4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_style_index_0_id_b530dfe4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_master_my_order_vue_vue_type_style_index_0_id_b530dfe4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 594:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?vue&type=style&index=0&id=b530dfe4&scoped=true&lang=scss& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[587,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../.sourcemap/mp-weixin/shifu/master_my_order.js.map