@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-b530dfe4 {
  background-color: #F8F8F8;
  height: 100vh;
  overflow: auto;
  padding-top: 100rpx;
}
.page .header.data-v-b530dfe4 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 750rpx;
  height: 100rpx;
  background: #FFFFFF;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.page .header .header_item.data-v-b530dfe4 {
  max-width: 85rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.page .header .header_item .blue.data-v-b530dfe4 {
  margin-top: 8rpx;
  width: 38rpx;
  height: 6rpx;
  background: #2E80FE;
  border-radius: 4rpx;
}
.page .main.data-v-b530dfe4 {
  padding: 20rpx 30rpx;
  min-height: calc(100vh - 100rpx);
}
.page .main .main_item.data-v-b530dfe4 {
  width: 690rpx;
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 28rpx 36rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}
.page .main .main_item .head.data-v-b530dfe4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .main .main_item .head .no.data-v-b530dfe4 {
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .order-type-info.data-v-b530dfe4 {
  margin-top: 15rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item .order-type-info .order-type-row.data-v-b530dfe4 {
  display: flex;
  align-items: center;
}
.page .main .main_item .order-type-info .order-type-row .order-type-label.data-v-b530dfe4 {
  font-size: 24rpx;
  color: #666666;
  margin-right: 8rpx;
}
.page .main .main_item .order-type-info .order-type-row .order-type-value.data-v-b530dfe4 {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}
.page .main .main_item .order-type-info .urgent-indicator .urgent-text.data-v-b530dfe4 {
  background: #FF6B35;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}
.page .main .main_item .mid.data-v-b530dfe4 {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item .mid .lef.data-v-b530dfe4 {
  display: flex;
  align-items: center;
}
.page .main .main_item .mid .lef image.data-v-b530dfe4 {
  width: 120rpx;
  height: 120rpx;
  flex-shrink: 0;
}
.page .main .main_item .mid .lef text.data-v-b530dfe4 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  margin-left: 30rpx;
  max-width: 350rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .mid .righ.data-v-b530dfe4 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  text-align: right;
  flex-shrink: 0;
}
.page .main .main_item .bot.data-v-b530dfe4 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 20rpx;
}
.page .main .main_item .bot .qzf.data-v-b530dfe4 {
  width: 148rpx;
  height: 48rpx;
  background: #2E80FE;
  border-radius: 50rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 48rpx;
  text-align: center;
  color: #fff;
  flex-shrink: 0;
}
.page .main .main_item .sub_orders.data-v-b530dfe4 {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1px solid #f0f0f0;
}
.page .main .main_item .sub_orders .sub_title.data-v-b530dfe4 {
  font-size: 26rpx;
  font-weight: 500;
  color: #666;
  margin-bottom: 20rpx;
}
.page .main .main_item .sub_orders .sub_scroll_container.data-v-b530dfe4 {
  max-height: 450rpx;
  overflow-y: auto;
}
.page .main .main_item .sub_orders .sub_item.data-v-b530dfe4 {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
}
.page .main .main_item .sub_orders .sub_item .sub_head.data-v-b530dfe4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}
.page .main .main_item .sub_orders .sub_item .sub_head .sub_no.data-v-b530dfe4 {
  font-size: 22rpx;
  color: #666;
  max-width: 400rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .sub_orders .sub_item .sub_head .sub_status.data-v-b530dfe4 {
  font-size: 22rpx;
  color: #2E80FE;
  font-weight: 500;
}
.page .main .main_item .sub_orders .sub_item .sub_content.data-v-b530dfe4 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info_grid.data-v-b530dfe4 {
  flex: 1;
  margin-right: 20rpx;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row.data-v-b530dfe4 {
  display: flex;
  margin-bottom: 12rpx;
  gap: 20rpx;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row.data-v-b530dfe4:last-child {
  margin-bottom: 0;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item.data-v-b530dfe4 {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item.sub_reason_item.data-v-b530dfe4 {
  flex: 2;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item .sub_label.data-v-b530dfe4 {
  font-size: 20rpx;
  color: #999;
  margin-right: 8rpx;
  flex-shrink: 0;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item .sub_value.data-v-b530dfe4 {
  font-size: 22rpx;
  color: #333;
  flex: 1;
  min-width: 0;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item .sub_value.sub_amount_value.data-v-b530dfe4 {
  font-weight: 500;
  color: #ff6b35;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item .sub_value.sub_warranty_value.data-v-b530dfe4 {
  color: #2E80FE;
  font-weight: 500;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item .sub_value.sub_reason_value.data-v-b530dfe4 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item .sub_value.sub_time_value.data-v-b530dfe4 {
  font-size: 20rpx;
  color: #999;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_actions.data-v-b530dfe4 {
  flex-shrink: 0;
  align-self: flex-start;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_actions .sub_qzf.data-v-b530dfe4 {
  width: 120rpx;
  height: 40rpx;
  background: #ff6b6b;
  border-radius: 40rpx;
  font-size: 18rpx;
  font-weight: 400;
  line-height: 40rpx;
  text-align: center;
  color: #fff;
}
.slot-content.data-v-b530dfe4 {
  padding: 30rpx;
}
.upload-container.data-v-b530dfe4 {
  margin-top: 16rpx;
  padding: 20rpx;
  border: 1rpx dashed #ccc;
  border-radius: 8rpx;
  background: #fafafa;
}
.parts-img.data-v-b530dfe4 {
  width: 120rpx;
  height: 120rpx;
  margin-right: 12rpx;
  margin-bottom: 12rpx;
  border-radius: 8rpx;
}
/* 简洁的差价申请弹窗样式 */
.diff-apply-modal.data-v-b530dfe4 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
.modal-content.data-v-b530dfe4 {
  width: 90%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
.modal-header.data-v-b530dfe4 {
  position: relative;
  padding: 40rpx 50rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.modal-title.data-v-b530dfe4 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}
.close-btn.data-v-b530dfe4 {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background: #f5f5f5;
  transition: background 0.2s;
}
.close-btn.data-v-b530dfe4:active {
  background: #e0e0e0;
}
.close-icon.data-v-b530dfe4 {
  font-size: 28rpx;
  color: #666;
  line-height: 1;
}
.modal-body.data-v-b530dfe4 {
  padding: 40rpx;
}
.modal-footer.data-v-b530dfe4 {
  padding: 20rpx 40rpx 40rpx;
  display: flex;
  gap: 20rpx;
}
.btn-cancel.data-v-b530dfe4,
.btn-confirm.data-v-b530dfe4 {
  flex: 1;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8rpx;
  font-size: 28rpx;
  transition: all 0.2s;
}
.btn-cancel.data-v-b530dfe4 {
  background: #f8f8f8;
  color: #666;
  border: 1rpx solid #ddd;
}
.btn-cancel.data-v-b530dfe4:active {
  background: #e8e8e8;
}
.btn-confirm.data-v-b530dfe4 {
  background: #007aff;
  color: #fff;
  border: 1rpx solid #007aff;
}
.btn-confirm.data-v-b530dfe4:active {
  background: #0056cc;
}
/* 简洁表单样式 */
.modal-body.data-v-b530dfe4 .u-form-item {
  margin-bottom: 24rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.modal-body.data-v-b530dfe4 .u-form-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}
.modal-body.data-v-b530dfe4 .u-form-item__label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}
.modal-body.data-v-b530dfe4 .u--input__content {
  background: #f8f8f8;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #333;
}
.modal-body.data-v-b530dfe4 .u--textarea__content {
  background: #f8f8f8;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #333;
  min-height: 120rpx;
}
/* 原因类型显示样式 */
.reason-type-display.data-v-b530dfe4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background: #f8f8f8;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
}
.reason-type-text.data-v-b530dfe4 {
  font-size: 28rpx;
  color: #333;
}
.reason-type-badge.data-v-b530dfe4 {
  background: #007aff;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

