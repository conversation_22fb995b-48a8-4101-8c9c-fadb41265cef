<view class="container"><view class="current-grade"><text class="current-label">当前等级:</text><text class="current-value">{{info.labelName}}</text></view><view class="upgrade-title">可升级选项:</view><view class="grade-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view class="{{['grade-item',(item.$orig.id===info.labelId)?'current-grade-item':'']}}"><view class="grade-header"><text class="grade-name">{{item.$orig.labelName}}</text></view><view class="grade-details"><view class="detail-item"><text class="detail-label">保证金:</text><text class="detail-value">{{"¥"+item.$orig.earnestMoney}}</text></view><block wx:if="{{item.$orig.id!==info.labelId}}"><view class="detail-item"><text class="detail-label">升级需支付:</text><text class="detail-value upgrade-price">{{"¥"+item.g0}}</text></view></block><block wx:else><view class="detail-item"><text class="detail-label">当前等级</text><text class="detail-value current-tag">✓</text></view></block></view><block wx:if="{{item.$orig.id!==info.labelId}}"><button class="upgrade-btn" disabled="{{item.$orig.upgradeDisabled}}" data-event-opts="{{[['tap',[['handleUpgrade',['$0'],[[['upgradeOptions','id',item.$orig.id]]]]]]]}}" bindtap="__e">{{''+(item.$orig.upgradeDisabled?'不可升级':'立即升级')+''}}</button></block></view></block></view></view>