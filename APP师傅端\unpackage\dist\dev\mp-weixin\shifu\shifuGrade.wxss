
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.current-grade {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.current-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}
.current-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #2d8cf0;
  margin-right: 10rpx;
}
.current-id {
  font-size: 24rpx;
  color: #999;
}
.upgrade-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin: 20rpx 0;
  padding-left: 10rpx;
}
.grade-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.grade-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}
.current-grade-item {
  border: 2rpx solid #2d8cf0;
  background-color: #f0f9ff;
}
.grade-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}
.grade-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.grade-id {
  font-size: 24rpx;
  color: #999;
}
.grade-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-bottom: 20rpx;
}
.detail-item {
  display: flex;
  flex-direction: column;
}
.detail-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}
.detail-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.upgrade-price {
  color: #f56c6c;
  font-weight: bold;
}
.current-tag {
  color: #67c23a;
  font-weight: bold;
}
.upgrade-btn {
  background-color: #2d8cf0;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 0;
  /* 减少上下 padding，降低高度 */
  font-size: 28rpx;
  line-height: 1.2;
  /* 调整行高，避免文字上下间距过大 */
  margin-top: 10rpx;
  width: 100%;
  height: auto;
  /* 确保高度自适应 */
  min-height: 60rpx;
  /* 设置最小高度（可选） */
}
.upgrade-btn[disabled] {
  background-color: #c8c9cc;
  color: #909399;
}

/* 根据不同的等级添加不同的颜色标识 */
.grade-item:nth-child(1) .grade-name {
  color: #ff6b81;
  /* 钻石师傅 - 粉色 */
}
.grade-item:nth-child(2) .grade-name {
  color: #ffa502;
  /* 金牌师傅 - 金色 */
}
.grade-item:nth-child(3) .grade-name {
  color: #a4b0be;
  /* 银牌师傅 - 银色 */
}
.grade-item:nth-child(4) .grade-name {
  color: #cd7f32;
  /* 铜牌师傅 - 铜色 */
}
.grade-item:nth-child(5) .grade-name {
  color: #57606f;
  /* 普通师傅 - 灰色 */
}

