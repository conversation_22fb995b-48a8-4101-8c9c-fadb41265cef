<view class="page data-v-2b3aa6dc"><view class="main data-v-2b3aa6dc"><view class="left data-v-2b3aa6dc"><scroll-view class="scrollL data-v-2b3aa6dc" scroll-y="true"><block wx:if="{{loading}}"><view class="loading data-v-2b3aa6dc"><text class="data-v-2b3aa6dc">加载中...</text></view></block><block wx:else><block wx:if="{{error}}"><view class="error data-v-2b3aa6dc"><text class="data-v-2b3aa6dc">{{error}}</text></view></block><block wx:else><block wx:if="{{!$root.g0}}"><view class="no-content data-v-2b3aa6dc"><text class="data-v-2b3aa6dc">暂无分类数据</text></view></block><block wx:else><block wx:for="{{categories}}" wx:for-item="category" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['selectCategory',['$0'],[[['categories','id',category.id,'id']]]]]]]}}" class="{{['left_item','data-v-2b3aa6dc',(selectedCategoryId===category.id)?'active':'']}}" bindtap="__e"><view class="category_name data-v-2b3aa6dc">{{category.name}}</view></view></block></block></block></block></scroll-view></view><view class="right data-v-2b3aa6dc"><scroll-view class="scrollR data-v-2b3aa6dc" scroll-y="true" scroll-top="{{scrollTop}}" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><block wx:if="{{loading}}"><view class="loading data-v-2b3aa6dc"><text class="data-v-2b3aa6dc">加载中...</text></view></block><block wx:else><block wx:if="{{error}}"><view class="error data-v-2b3aa6dc"><text class="data-v-2b3aa6dc">{{error}}</text></view></block><block wx:else><block wx:if="{{$root.g1}}"><view class="data-v-2b3aa6dc"><view class="category_header data-v-2b3aa6dc"><view class="category_info data-v-2b3aa6dc"><view class="category_title data-v-2b3aa6dc">{{currentCategory.name}}</view><view class="category_stats data-v-2b3aa6dc"><text class="total_count data-v-2b3aa6dc">{{"共"+$root.g2+"项"}}</text><text class="selected_count data-v-2b3aa6dc">{{"已选"+$root.m0+"项"}}</text></view></view><view data-event-opts="{{[['tap',[['toggleSelectAll']]]]}}" class="select_all_btn data-v-2b3aa6dc" bindtap="__e"><text class="select_all_text data-v-2b3aa6dc">{{$root.m1?'取消全选':'全选'}}</text></view></view><view class="subcategory_grid data-v-2b3aa6dc"><block wx:for="{{currentCategory.children}}" wx:for-item="subCategory" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['toggleSelect',['$0'],[[['currentCategory.children','id',subCategory.id]]]]]]]}}" class="{{['subcategory_item','data-v-2b3aa6dc',(subCategory.selected)?'active':'']}}" bindtap="__e"><text class="subcategory_name data-v-2b3aa6dc">{{subCategory.name}}</text><block wx:if="{{subCategory.selected}}"><view class="selected_icon data-v-2b3aa6dc"><text class="data-v-2b3aa6dc">✓</text></view></block></view></block></view></view></block><block wx:else><view class="no-content data-v-2b3aa6dc"><text class="data-v-2b3aa6dc">暂无子分类</text></view></block></block></block></scroll-view></view></view><view class="footer data-v-2b3aa6dc"><button data-event-opts="{{[['tap',[['saveSettings',['$event']]]]]}}" class="save_btn data-v-2b3aa6dc" bindtap="__e">保存设置</button></view></view>