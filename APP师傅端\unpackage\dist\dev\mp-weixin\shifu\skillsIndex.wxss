
.page.data-v-6c7d5886 {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}
.main.data-v-6c7d5886 {
  flex: 1;
  display: flex;
  overflow: hidden;
}
.left.data-v-6c7d5886 {
  width: 200rpx;
  background: #f8f9fa;
  border-right: 1rpx solid #dee2e6;
}
.scrollL.data-v-6c7d5886 {
  height: 100%;
}
.left_item.data-v-6c7d5886 {
  padding: 30rpx 20rpx;
  font-size: 28rpx;
  text-align: center;
  border-left: 4rpx solid transparent;
}
.left_item.active.data-v-6c7d5886 {
  color: #2e80fe;
  background: #e6f0ff;
  border-left-color: #2e80fe;
}
.right.data-v-6c7d5886 {
  flex: 1;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
.scrollR.data-v-6c7d5886 {
  flex: 1;
  height: 100%;
}
.category_header.data-v-6c7d5886 {
  padding: 20rpx;
  background: #2e80fe;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.category_title.data-v-6c7d5886 {
  font-size: 32rpx;
  font-weight: bold;
}
.category_stats.data-v-6c7d5886 {
  font-size: 24rpx;
  opacity: 0.9;
}
.select_all_btn.data-v-6c7d5886 {
  padding: 10rpx 20rpx;
  background: rgba(255,255,255,0.2);
  border-radius: 30rpx;
}

/* 改为网格布局 */
.subcategory_grid.data-v-6c7d5886 {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx;
}
.subcategory_item.data-v-6c7d5886 {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  padding: 25rpx 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
}
.subcategory_item.active.data-v-6c7d5886 {
  background: #e6f0ff;
  color: #2e80fe;
  border: 1rpx solid #2e80fe;
}
.subcategory_name.data-v-6c7d5886 {
  font-size: 28rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
}
.selected_icon.data-v-6c7d5886 {
  color: #2e80fe;
  font-weight: bold;
}
.footer.data-v-6c7d5886 {
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
}
.save_btn.data-v-6c7d5886 {
  background: #2e80fe;
  color: white;
  border-radius: 50rpx;
}
.loading.data-v-6c7d5886, .error.data-v-6c7d5886, .no-content.data-v-6c7d5886 {
  padding: 30rpx;
  text-align: center;
  color: #999;
}

