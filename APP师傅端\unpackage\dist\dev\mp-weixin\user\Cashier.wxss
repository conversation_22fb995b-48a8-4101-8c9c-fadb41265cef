@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-5b297b6d {
  background-color: #f8f8f8;
  height: 100vh;
  padding-top: 40rpx;
}
.page .choose_yh.data-v-5b297b6d {
  padding-top: 40rpx;
  width: 750rpx;
  height: 1106rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 0rpx 0rpx;
  opacity: 1;
  position: fixed;
  bottom: 0;
  z-index: 10088;
  transition: all 0.5s;
}
.page .choose_yh .head.data-v-5b297b6d {
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
  text-align: center;
  margin-bottom: 44rpx;
}
.page .choose_yh .close.data-v-5b297b6d {
  position: absolute;
  top: 44rpx;
  right: 32rpx;
}
.page .choose_yh .close image.data-v-5b297b6d {
  width: 37rpx;
  height: 37rpx;
}
.page .choose_yh .cou_item.data-v-5b297b6d {
  margin: 0 auto;
  width: 690rpx;
  height: 202rpx;
  background: #DCEAFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #2E80FE;
}
.page .choose_yh .cou_item .top.data-v-5b297b6d {
  height: 150rpx;
  display: flex;
  align-items: center;
  padding-top: 26rpx;
  padding-left: 24rpx;
  padding-right: 14rpx;
  position: relative;
  border-bottom: 2rpx dashed #2E80FE;
}
.page .choose_yh .cou_item .top .box1.data-v-5b297b6d {
  text-align: center;
  width: 185rpx;
  font-size: 40rpx;
  font-weight: 500;
  color: #E72427;
}
.page .choose_yh .cou_item .top .box1 ._span.data-v-5b297b6d {
  font-size: 15rpx;
}
.page .choose_yh .cou_item .top .box2.data-v-5b297b6d {
  margin-left: 28rpx;
}
.page .choose_yh .cou_item .top .box2 text.data-v-5b297b6d {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
  max-width: 450rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .choose_yh .cou_item .top .box2 ._span.data-v-5b297b6d {
  margin-top: 10rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #B2B2B2;
}
.page .choose_yh .cou_item .top .box3.data-v-5b297b6d {
  position: absolute;
  right: 22rpx;
  top: 40rpx;
  width: 40rpx;
  height: 40rpx;
  background: #fff;
  border: 2rpx solid #B2B2B2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page .choose_yh .cou_item .bottom.data-v-5b297b6d {
  padding: 0 24rpx;
  height: 50rpx;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 50rpx;
  font-size: 20rpx;
  font-weight: 400;
  color: #B2B2B2;
}
.page .choose_yh .noYh.data-v-5b297b6d {
  width: 690rpx;
  margin: 0 auto;
  margin-top: 52rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 22rpx;
}
.page .choose_yh .noYh .left.data-v-5b297b6d {
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .choose_yh .noYh .right.data-v-5b297b6d {
  width: 40rpx;
  height: 40rpx;
  background: #fff;
  border: 2rpx solid #B2B2B2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page .choose_yh .notcan.data-v-5b297b6d {
  margin-top: 52rpx;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #B2B2B2;
  padding: 0 30rpx;
}
.page .time.data-v-5b297b6d {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.page .time text.data-v-5b297b6d {
  margin-right: 15rpx;
}
.page .price.data-v-5b297b6d {
  display: inline-block;
  width: 750rpx;
  text-align: center;
  margin-top: 20rpx;
  font-size: 80rpx;
  font-weight: 500;
  color: #292C39;
}
.page .price ._span.data-v-5b297b6d {
  font-size: 36rpx;
}
.page .payCard.data-v-5b297b6d {
  margin: 0 auto;
  width: 686rpx;
  height: 130rpx;
  background: #FFFFFF;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  margin-top: 40rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.page .payCard .left.data-v-5b297b6d {
  display: flex;
  align-items: center;
}
.page .payCard .left image.data-v-5b297b6d {
  width: 70rpx;
  height: 70rpx;
}
.page .payCard .left text.data-v-5b297b6d {
  margin-left: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .payCard .right.data-v-5b297b6d {
  display: flex;
  align-items: center;
}
.page .payCard .right text.data-v-5b297b6d {
  font-size: 24rpx;
  font-weight: 400;
  color: #E72427;
}
.page .payCard .choose.data-v-5b297b6d {
  width: 40rpx;
  height: 40rpx;
  background-color: #fff;
  border: 2rpx solid #ADADAD;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page .footer.data-v-5b297b6d {
  width: 750rpx;
  height: 192rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 0;
}
.page .footer .btn.data-v-5b297b6d {
  width: 686rpx;
  height: 88rpx;
  background: #2E80FE;
  border-radius: 44rpx 44rpx 44rpx 44rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 88rpx;
  text-align: center;
}

