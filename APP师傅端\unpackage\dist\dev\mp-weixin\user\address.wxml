<view class="page data-v-4df5043e"><block wx:if="{{$root.g0>0}}"><view class="data-v-4df5043e"><block wx:for="{{addressArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['choose',['$0'],[[['addressArr','',index]]]]]]]}}" class="address_item data-v-4df5043e" bindtap="__e"><view class="head data-v-4df5043e"><block wx:if="{{item.status==1}}"><view class="mr data-v-4df5043e">默认</view></block><text class="data-v-4df5043e">{{item.address}}</text><label class="_span data-v-4df5043e"></label></view><view class="body data-v-4df5043e"><view class="left data-v-4df5043e">{{item.addressInfo}}</view><view data-event-opts="{{[['tap',[['goUrl',['$0'],[[['addressArr','',index]]]]]]]}}" class="right data-v-4df5043e" catchtap="__e"><image src="../static/images/9369.png" mode class="data-v-4df5043e"></image></view></view><view class="foot data-v-4df5043e"><view class="box data-v-4df5043e">{{item.userName}}</view><view class="box data-v-4df5043e">{{item.sex==1?'（先生）':'（女士）'}}</view><view class="box data-v-4df5043e">{{item.mobile}}</view></view></view></block></view></block><block wx:if="{{$root.g1<total}}"><view data-event-opts="{{[['tap',[['loadMore',['$event']]]]]}}" class="load-more data-v-4df5043e" bindtap="__e">加载更多</view></block><view class="footer data-v-4df5043e"><view data-event-opts="{{[['tap',[['goUrlAdd',['../user/add_address']]]]]}}" class="btn data-v-4df5043e" bindtap="__e">新增地址</view></view></view>