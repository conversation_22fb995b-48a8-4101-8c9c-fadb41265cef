<view class="page data-v-988c8adc"><u-modal vue-id="7903ce98-1" show="{{show}}" title="删除银行卡" content="确认要删除该银行卡吗？" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e0']]],['^confirm',[['confirm']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-988c8adc" bind:__l="__l"></u-modal><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['clickItem',['$0'],[[['list','',index]]]]]]]}}" class="card_item data-v-988c8adc" bindtap="__e"><view class="title data-v-988c8adc">{{item.bankName}}</view><view class="num data-v-988c8adc">{{item.cardNo}}</view><view data-event-opts="{{[['tap',[['trashOne',['$0'],[[['list','',index]]]]]]]}}" class="trash data-v-988c8adc" catchtap="__e"><uni-icons vue-id="{{'7903ce98-2-'+index}}" type="trash-filled" size="30" color="#fff" class="data-v-988c8adc" bind:__l="__l"></uni-icons></view></view></block><view data-event-opts="{{[['tap',[['goUrl',['../user/addcard']]]]]}}" class="add data-v-988c8adc" bindtap="__e"><view class="left data-v-988c8adc"><image src="../static/images/9582.png" mode class="data-v-988c8adc"></image><text class="data-v-988c8adc">添加银行卡</text></view><view class="right data-v-988c8adc"><u-icon vue-id="7903ce98-3" name="arrow-right-double" color="#999999" size="18" class="data-v-988c8adc" bind:__l="__l"></u-icon></view></view></view>