<view class="page data-v-9870f036"><view class="header data-v-9870f036"><view class="left data-v-9870f036">提现至</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="right data-v-9870f036" bindtap="__e">{{''+$root.m0+''}}<text class="arrow data-v-9870f036">></text></view></view><block wx:if="{{cashToType===2}}"><view class="alipay-info data-v-9870f036"><view class="info-item data-v-9870f036"><view class="label data-v-9870f036">支付宝手机号</view><u--input bind:input="__e" vue-id="75ac147e-1" placeholder="请输入支付宝登录手机号" border="none" value="{{alipayPhone}}" data-event-opts="{{[['^input',[['__set_model',['','alipayPhone','$event',[]]]]]]}}" class="data-v-9870f036" bind:__l="__l"></u--input></view><view class="info-item data-v-9870f036"><view class="label data-v-9870f036">真实姓名</view><u--input bind:input="__e" vue-id="75ac147e-2" placeholder="请输入真实姓名" border="none" value="{{realName}}" data-event-opts="{{[['^input',[['__set_model',['','realName','$event',[]]]]]]}}" class="data-v-9870f036" bind:__l="__l"></u--input></view><view class="info-item data-v-9870f036"><view class="label data-v-9870f036">身份证号</view><u--input bind:input="__e" vue-id="75ac147e-3" placeholder="请输入身份证号" border="none" value="{{idCode}}" data-event-opts="{{[['^input',[['__set_model',['','idCode','$event',[]]]]]]}}" class="data-v-9870f036" bind:__l="__l"></u--input></view></view></block><view class="mid data-v-9870f036"><view class="title data-v-9870f036">提现金额</view><view class="top data-v-9870f036"><view class="t_left data-v-9870f036"><u--input vue-id="75ac147e-4" placeholder="请输入提现金额" type="digit" border="none" prefixIcon="rmb" value="{{money}}" data-event-opts="{{[['^change',[['change']]],['^input',[['__set_model',['','money','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" class="data-v-9870f036" bind:__l="__l"></u--input></view><view data-event-opts="{{[['tap',[['goAll',['$event']]]]]}}" class="r_left data-v-9870f036" bindtap="__e">全部提现</view></view><view class="bottom data-v-9870f036">{{"可提现金额￥"+allmoney}}</view></view><view class="btn data-v-9870f036" disabled="{{isSubmitting}}" data-event-opts="{{[['tap',[['confirmTx',['$event']]]]]}}" bindtap="__e">确认提现</view><text class="tips data-v-9870f036">温馨提示：提现申请发起后，预计3个工作日内到账。</text><text class="contact data-v-9870f036">有问题请联系客服<text data-event-opts="{{[['tap',[['copyPhoneNumber',['$event']]]]]}}" class="phone data-v-9870f036" bindtap="__e">4008326986</text></text><u-popup vue-id="75ac147e-5" show="{{showCashToType}}" mode="bottom" round="{{10}}" closeable="{{true}}" data-event-opts="{{[['^close',[['e1']]]]}}" bind:close="__e" class="data-v-9870f036" bind:__l="__l" vue-slots="{{['default']}}"><view class="cash-type-modal data-v-9870f036"><view class="modal-header data-v-9870f036"><view class="modal-title data-v-9870f036">选择提现渠道</view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="modal-close data-v-9870f036" bindtap="__e"></view></view><view class="cash-type-list data-v-9870f036"><block wx:if="{{isWeixinMiniProgram}}"><view data-event-opts="{{[['tap',[['selectCashToType',[1]]]]]}}" class="{{['cash-type-item','data-v-9870f036',(cashToType===1)?'active':'']}}" bindtap="__e"><view class="type-name data-v-9870f036">微信</view><block wx:if="{{cashToType===1}}"><view class="type-check data-v-9870f036">✓</view></block></view></block><view data-event-opts="{{[['tap',[['selectCashToType',[2]]]]]}}" class="{{['cash-type-item','data-v-9870f036',(cashToType===2)?'active':'']}}" bindtap="__e"><view class="type-name data-v-9870f036">支付宝</view><block wx:if="{{cashToType===2}}"><view class="type-check data-v-9870f036">✓</view></block></view><view data-event-opts="{{[['tap',[['selectCashToType',[3]]]]]}}" class="{{['cash-type-item','data-v-9870f036',(cashToType===3)?'active':'']}}" bindtap="__e"><view class="type-name data-v-9870f036">银行卡</view><block wx:if="{{cashToType===3}}"><view class="type-check data-v-9870f036">✓</view></block></view></view></view></u-popup></view>