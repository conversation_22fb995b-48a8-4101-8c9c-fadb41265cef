@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-20f62b46 {
  width: 100%;
}
.page .header.data-v-20f62b46 {
  padding: 24rpx 30rpx;
  background-color: #fff;
}
.page .header .nowP.data-v-20f62b46 {
  height: 40rpx;
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
}
.page .header .nowP .left.data-v-20f62b46 {
  display: flex;
  align-items: center;
}
.page .header .nowP .left ._span.data-v-20f62b46 {
  font-weight: 400;
  color: #999999;
  font-size: 20rpx;
}
.page .header .nowP .right.data-v-20f62b46 {
  color: #2E80FE;
  font-weight: 400;
  font-size: 28rpx;
}
.page .main.data-v-20f62b46 .u-index-list__letter {
  top: 140px !important;
}
.page .main .head.data-v-20f62b46 {
  height: 400rpx;
  padding: 20rpx 30rpx;
  background-color: #F8F8F8;
}
.page .main .head .last.data-v-20f62b46 {
  height: 140rpx;
}
.page .main .head .last text.data-v-20f62b46 {
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .main .head .last .last-box.data-v-20f62b46 {
  display: flex;
  flex-wrap: wrap;
}
.page .main .head .last .last-box .box-item.data-v-20f62b46 {
  width: -webkit-fit-content;
  width: fit-content;
  min-width: 210rpx;
  height: 68rpx;
  margin: 20rpx 7rpx;
  background: #FFFFFF;
  line-height: 68rpx;
  text-align: center;
}
.page .main .head .hot.data-v-20f62b46 {
  height: 260rpx;
}
.page .main .head .hot text.data-v-20f62b46 {
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  margin-bottom: 20rpx;
}
.page .main .head .hot .hot-box.data-v-20f62b46 {
  display: flex;
  flex-wrap: wrap;
}
.page .main .head .hot .hot-box .box-item.data-v-20f62b46 {
  padding: 0 10rpx;
  width: -webkit-fit-content;
  width: fit-content;
  min-width: 210rpx;
  margin: 20rpx 7rpx;
  background: #FFFFFF;
  line-height: 68rpx;
  text-align: center;
}
.page .main .list-cell.data-v-20f62b46 {
  display: flex;
  box-sizing: border-box;
  width: 100%;
  padding: 10px 24rpx;
  overflow: hidden;
  color: #323233;
  font-size: 14px;
  line-height: 24px;
  background-color: #fff;
}

