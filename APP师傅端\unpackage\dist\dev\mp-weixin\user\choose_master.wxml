<view class="page data-v-105ecd58"><u-modal vue-id="02431232-1" show="{{show}}" content="{{content}}" showCancelButton="{{true}}" cancelText="再想想" data-event-opts="{{[['^cancel',[['e0']]],['^confirm',[['confirmMaster']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-105ecd58" bind:__l="__l"></u-modal><view class="header data-v-105ecd58"><view class="title data-v-105ecd58">等待您选择师傅</view><view class="desc data-v-105ecd58">师傅可能在服务，请耐心等待</view><view class="time data-v-105ecd58">距师傅报价截止还剩：<u-count-down vue-id="02431232-2" time="{{24*60*60*1000}}" format="HH:mm:ss" class="data-v-105ecd58" bind:__l="__l"></u-count-down></view></view><view class="main data-v-105ecd58"><scroll-view scroll-y="true" class="data-v-105ecd58"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="main_item data-v-105ecd58"><view class="time data-v-105ecd58">{{"报价时间"+item.g0}}</view><view class="box data-v-105ecd58"><image src="{{item.$orig.selfImg?item.$orig.selfImg:'/static/mine/default_user.png'}}" mode="aspectFit" class="data-v-105ecd58"></image><view class="mid data-v-105ecd58"><view class="top data-v-105ecd58"><view class="name data-v-105ecd58">{{item.$orig.coachName}}</view><block wx:if="{{item.$orig.labelName!=null}}"><view class="level data-v-105ecd58">{{item.$orig.labelName}}</view></block><block wx:if="{{item.$orig.cashPledge!=null}}"><view class="promise data-v-105ecd58">已缴纳保证金</view></block></view><view class="bottom data-v-105ecd58">服务<label class="_span data-v-105ecd58">{{item.$orig.count}}</label>次</view></view><view class="price data-v-105ecd58">{{"￥"+item.g1}}</view></view><view class="down data-v-105ecd58"><view data-event-opts="{{[['tap',[['chooseOne',['$0'],[[['info.quotedPriceVos','',index]]]]]]]}}" class="btn data-v-105ecd58" bindtap="__e">选择他</view></view></view></block></scroll-view></view><view class="footer data-v-105ecd58"><view data-event-opts="{{[['tap',[['cancelO',['$event']]]]]}}" class="btn data-v-105ecd58" bindtap="__e">取消订单</view></view><u-modal vue-id="02431232-3" show="{{showCancel}}" title="取消订单" content="确认要取消该订单吗" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e1']]],['^confirm',[['confirmCancel']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-105ecd58" bind:__l="__l"></u-modal></view>