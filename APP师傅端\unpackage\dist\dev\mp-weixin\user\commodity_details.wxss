@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-4d2a26fa {
  padding-bottom: 166rpx;
}
.page.data-v-4d2a26fa  .u-popup__content {
  display: none;
}
.page .fg.data-v-4d2a26fa {
  height: 20rpx;
  background: #f0f0f0;
}
.page .choose_box.data-v-4d2a26fa {
  width: 750rpx;
  height: 692rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 0rpx 0rpx;
  position: fixed;
  bottom: 0;
  z-index: 10076;
  transition: all 0.5s;
}
.page .choose_box .head.data-v-4d2a26fa {
  margin-top: 40rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .choose_box .close.data-v-4d2a26fa {
  position: absolute;
  top: 44rpx;
  right: 32rpx;
}
.page .choose_box .close image.data-v-4d2a26fa {
  width: 37rpx;
  height: 37rpx;
}
.page .choose_box .choose_item.data-v-4d2a26fa {
  width: 686rpx;
  height: 200rpx;
  position: relative;
  margin: 0 auto;
  margin-top: 40rpx;
}
.page .choose_box .choose_item image.data-v-4d2a26fa {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: -1;
}
.page .choose_box .choose_item .title.data-v-4d2a26fa {
  padding-top: 40rpx;
  padding-left: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #824109;
}
.page .choose_box .choose_item .ctx.data-v-4d2a26fa {
  max-width: 524rpx;
  margin-top: 16rpx;
  padding-left: 40rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #a38071;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
}
.page .header image.data-v-4d2a26fa {
  width: 750rpx;
  height: 620rpx;
}
.page .header .Info.data-v-4d2a26fa {
  padding: 40rpx 32rpx;
}
.page .header .Info .title.data-v-4d2a26fa {
  max-width: 550rpx;
  font-size: 40rpx;
  font-weight: 500;
  color: #171717;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .header .Info .price.data-v-4d2a26fa {
  margin-top: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #e72427;
}
.page .header .Info .num.data-v-4d2a26fa {
  margin-top: 12rpx;
  font-size: 24rpx;
  fontgroups-weight: 400;
  color: #999999;
}
.page .site.data-v-4d2a26fa {
  padding: 40rpx 32rpx;
}
.page .site .top.data-v-4d2a26fa {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .site .top .left.data-v-4d2a26fa {
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .site .top .right.data-v-4d2a26fa {
  font-size: 28rpx;
  font-weight: 400;
  color: #2e80fe;
}
.page .site .site_box .site_item.data-v-4d2a26fa {
  margin-top: 40rpx;
  display: flex;
  border-bottom: 2rpx solid #e9e9e9;
}
.page .site .site_box .site_item image.data-v-4d2a26fa {
  width: 202rpx;
  height: 142rpx;
  margin-right: 20rpx;
}
.page .site .site_box .site_item .content.data-v-4d2a26fa {
  flex: 1;
}
.page .site .site_box .site_item .content .name.data-v-4d2a26fa {
  width: 464rpx;
  min-height: 76rpx;
  font-size: 28rpx;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
}
.page .site .site_box .site_item .content .address.data-v-4d2a26fa {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .site .site_box .site_item .content .address .position.data-v-4d2a26fa {
  display: flex;
  align-items: center;
}
.page .site .site_box .site_item .content .address .position text.data-v-4d2a26fa {
  font-size: 24rpx;
  color: #333333;
  max-width: 320rpx;
  white-space: normal;
  margin-right: 10rpx;
}
.page .details.data-v-4d2a26fa {
  padding: 20rpx 30rpx;
}
.page .details .ddd.data-v-4d2a26fa {
  width: 100%;
  overflow: hidden;
}
.page .details .ddd ._img.data-v-4d2a26fa {
  max-width: 100% !important;
  height: auto !important;
  display: block;
}
.page .eva.data-v-4d2a26fa {
  padding: 20rpx 30rpx;
}
.page .eva .top.data-v-4d2a26fa {
  padding-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 2rpx solid #dddddd;
}
.page .eva .top .left.data-v-4d2a26fa {
  font-size: 28rpx;
  font-weight: 400;
  color: #3b3b3b;
}
.page .eva .top .right.data-v-4d2a26fa {
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
}
.page .eva .eva_item.data-v-4d2a26fa {
  padding: 20rpx 0;
  border-bottom: 2rpx solid #dddddd;
}
.page .eva .eva_item .top.data-v-4d2a26fa {
  display: flex;
  align-items: center;
  border: none;
  padding-bottom: 0;
}
.page .eva .eva_item .top image.data-v-4d2a26fa {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 12rpx;
  cursor: pointer;
}
.page .eva .eva_item .ctx.data-v-4d2a26fa {
  margin-top: 18rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
}
.page .eva .eva_item .eva_images.data-v-4d2a26fa {
  margin-top: 18rpx;
  display: flex;
  flex-wrap: wrap;
}
.page .eva .eva_item .eva_images image.data-v-4d2a26fa {
  cursor: pointer;
  border-radius: 8rpx;
}
.page .footer.data-v-4d2a26fa {
  padding: 38rpx 32rpx;
  width: 750rpx;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
  position: fixed;
  bottom: 0;
  display: flex;
  align-items: center;
}
.page .footer .righ.data-v-4d2a26fa {
  width: 690rpx;
  height: 88rpx;
  background: #2e80fe;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #ffffff;
  line-height: 88rpx;
  text-align: center;
}

