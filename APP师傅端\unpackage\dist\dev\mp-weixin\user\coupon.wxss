@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-423579cc {
  height: 100vh;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
}
.header.data-v-423579cc {
  background-color: #fff;
  height: 100rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid #f6f6f6;
}
.header .header-item .tag.data-v-423579cc {
  width: 38rpx;
  height: 6rpx;
  background: #2e80fe;
  border-radius: 4rpx;
  margin: auto;
  margin-top: 10rpx;
}
.main.data-v-423579cc {
  flex: 1;
  padding: 40rpx 30rpx;
  overflow-y: auto;
}
.main .main_item.data-v-423579cc {
  width: 690rpx;
  height: 202rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}
.main .main_item .top.data-v-423579cc {
  height: 150rpx;
  display: flex;
  align-items: center;
  padding-top: 26rpx;
  padding-left: 24rpx;
  padding-right: 14rpx;
  position: relative;
  border-bottom: 2rpx solid #e9e9e9;
}
.main .main_item .top .box1.data-v-423579cc {
  text-align: center;
  width: 185rpx;
  font-size: 40rpx;
  font-weight: 500;
  color: #e72427;
}
.main .main_item .top .box1 ._span.data-v-423579cc {
  font-size: 15rpx;
}
.main .main_item .top .box2.data-v-423579cc {
  margin-left: 28rpx;
}
.main .main_item .top .box2 text.data-v-423579cc {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
  max-width: 450rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.main .main_item .top .box2 ._span.data-v-423579cc {
  margin-top: 10rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #b2b2b2;
}
.main .main_item .top .box3.data-v-423579cc {
  position: absolute;
  right: 24rpx;
  top: 24rpx;
  width: 100rpx;
  height: 42rpx;
  background: #2e80fe;
  border-radius: 22rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: #ffffff;
  line-height: 42rpx;
  text-align: center;
}
.main .main_item .bottom.data-v-423579cc {
  padding: 0 24rpx;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 50rpx;
  font-size: 20rpx;
  font-weight: 400;
  color: #b2b2b2;
}
.main .loading-status.data-v-423579cc {
  text-align: center;
  padding: 40rpx 0;
  color: #999999;
  font-size: 28rpx;
}
.main .empty-data.data-v-423579cc {
  text-align: center;
  padding: 200rpx 0;
  color: #999999;
  font-size: 32rpx;
}

