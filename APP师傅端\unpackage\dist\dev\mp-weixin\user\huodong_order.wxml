<view class="page data-v-f26050c2"><u-modal vue-id="532b9d47-1" show="{{showChoose||showYh}}" content="{{content}}" class="data-v-f26050c2" bind:__l="__l"></u-modal><view class="choose_yh data-v-f26050c2" style="{{(showYh?'':'height:0')}}"><view class="head data-v-f26050c2">优惠券</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="close data-v-f26050c2" bindtap="__e"><image src="../static/images/9397.png" mode class="data-v-f26050c2"></image></view><block wx:if="{{$root.g0}}"><u-empty vue-id="532b9d47-2" mode="coupon" icon="http://cdn.uviewui.com/uview/empty/coupon.png" class="data-v-f26050c2" bind:__l="__l"></u-empty></block><block wx:else><scroll-view style="height:832rpx;" scroll-y="true" class="data-v-f26050c2"><block wx:for="{{couponlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cou_item data-v-f26050c2"><view class="top data-v-f26050c2"><block wx:if="{{item.type==0}}"><view class="box1 data-v-f26050c2"><label class="_span data-v-f26050c2">满</label>{{item.full}}<label class="_span data-v-f26050c2">减</label>{{item.discount+''}}</view></block><block wx:else><view class="box1 data-v-f26050c2"><label class="_span data-v-f26050c2">￥</label>{{item.discount}}</view></block><view class="box2 data-v-f26050c2"><text class="data-v-f26050c2">{{item.title}}</text><block wx:if="{{item.start_time==0}}"><label class="_span data-v-f26050c2">{{"有效期：自领券日起"+item.day+"天"}}</label></block><block wx:else><label class="_span data-v-f26050c2">{{"有效期："+item.start_time}}</label></block></view><view data-event-opts="{{[['tap',[['chooseItemyh',['$0'],[[['couponlist','',index]]]]]]]}}" class="box3 data-v-f26050c2" style="{{(item.choose?'background:#2E80FE;border:2rpx solid #2E80FE':'')}}" bindtap="__e"><u-icon vue-id="{{'532b9d47-3-'+index}}" name="checkbox-mark" color="#fff" size="16" class="data-v-f26050c2" bind:__l="__l"></u-icon></view></view><view class="bottom data-v-f26050c2">{{''+item.rule+''}}</view></view></block><view class="noYh data-v-f26050c2"><view class="left data-v-f26050c2">不使用优惠券</view><view data-event-opts="{{[['tap',[['chooseNotyh']]]]}}" class="right data-v-f26050c2" style="{{(notYh?'background:#2E80FE;border:2rpx solid #2E80FE':'')}}" bindtap="__e"><u-icon vue-id="532b9d47-4" name="checkbox-mark" color="#fff" size="16" class="data-v-f26050c2" bind:__l="__l"></u-icon></view></view><view class="notcan data-v-f26050c2">不可使用优惠券</view><block wx:for="{{nocouponlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cou_item data-v-f26050c2" style="border:2rpx solid #ADADAD;background:#fff;"><view class="top data-v-f26050c2" style="border-bottom:2rpx dashed #ADADAD;"><block wx:if="{{item.type==0}}"><view class="box1 data-v-f26050c2" style="color:#ADADAD;"><label class="_span data-v-f26050c2">满</label>{{item.full}}<label class="_span data-v-f26050c2">减</label>{{item.discount+''}}</view></block><view class="box1 data-v-f26050c2" style="color:#ADADAD;"><label class="_span data-v-f26050c2">￥</label>{{item.discount}}</view><view class="box2 data-v-f26050c2"><text style="color:#ADADAD;" class="data-v-f26050c2">{{item.title}}</text><block wx:if="{{item.start_time==0}}"><label class="_span data-v-f26050c2">{{"有效期：自领券日起"+item.day+"天"}}</label></block><block wx:else><label class="_span data-v-f26050c2">{{"生效时间："+item.start_time}}</label></block></view></view><view class="bottom data-v-f26050c2">{{''+item.rule+''}}</view></view></block></scroll-view></block></view><view data-event-opts="{{[['tap',[['goUrl',['$event']]]]]}}" class="address data-v-f26050c2" bindtap="__e"><view class="left data-v-f26050c2"><view class="top data-v-f26050c2"><image src="../static/images/position.png" mode class="data-v-f26050c2"></image><text style="color:#599eff;" class="data-v-f26050c2">{{mrAddress.address?mrAddress.address:'请先添加地址哦'}}</text></view><view class="bottom data-v-f26050c2">{{mrAddress.address?mrAddress.userName+mrAddress.mobile:''}}</view></view><u-icon vue-id="532b9d47-5" name="arrow-right" color="#333333" size="14" class="data-v-f26050c2" bind:__l="__l"></u-icon></view><view class="fg data-v-f26050c2"></view><view class="main data-v-f26050c2"><block wx:for="{{newItemArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="main_item data-v-f26050c2"><image src="{{item.cover}}" mode class="data-v-f26050c2"></image><view class="right data-v-f26050c2"><view class="title data-v-f26050c2">{{item.title}}</view><block wx:if="{{type==0}}"><view class="price data-v-f26050c2"><text class="data-v-f26050c2">{{"￥"+huodongdata.payPrice+"/台"}}</text></view></block></view></view></block><block wx:if="{{needShow}}"><view data-event-opts="{{[['tap',[['expandAll',['$event']]]]]}}" class="expand data-v-f26050c2" bindtap="__e">{{''+(showEx?'展开详情':'收起')+''}}<view class="icon_box data-v-f26050c2"><u-icon vue-id="532b9d47-6" name="{{showEx?'arrow-down':'arrow-up'}}" color="#ADADAD" size="14" class="data-v-f26050c2" bind:__l="__l"></u-icon></view></view></block></view><view class="fg data-v-f26050c2"></view><view class="notes data-v-f26050c2"><view class="title data-v-f26050c2">服务备注</view><textarea cols="25" rows="5" placeholder="想要额外嘱咐工作人员的可以备注哦~" data-event-opts="{{[['input',[['__set_model',['','notes','$event',[]]]]]]}}" value="{{notes}}" bindinput="__e" class="data-v-f26050c2"></textarea></view><view class="fg data-v-f26050c2"></view><view class="footer data-v-f26050c2"><view class="left data-v-f26050c2"></view><view class="mid data-v-f26050c2"></view><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="{{['right','data-v-f26050c2',(isSubmitting)?'disabled':'']}}" bindtap="__e">{{''+(isSubmitting?'提交中...':'立即下单')+''}}</view></view></view>