<view class="page data-v-0f872228"><view class="header data-v-0f872228"><image src="{{serviceInfo.cover}}" mode="scaleToFill" class="data-v-0f872228"></image></view><view class="content data-v-0f872228"><view class="card data-v-0f872228"><view class="top data-v-0f872228"><view class="title data-v-0f872228">{{serviceInfo.title}}</view><block wx:if="{{serviceInfo.servicePriceType!=1}}"><view class="price data-v-0f872228">{{"￥"+serviceInfo.price}}</view></block></view><view class="bottom data-v-0f872228"><view class="left data-v-0f872228">已选：</view><view class="right data-v-0f872228"><block wx:for="{{chooseArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tag data-v-0f872228">{{item.name}}</view></block></view></view></view><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="chol data-v-0f872228"><view class="choose data-v-0f872228"><view class="title data-v-0f872228"><block wx:if="{{item.isRequired==1}}"><label class="_span data-v-0f872228">*</label></block>{{item.problemDesc}}</view><view class="desc data-v-0f872228">{{item.problemContent}}</view><view class="cho_box data-v-0f872228"><block wx:for="{{item.options}}" wx:for-item="newItem" wx:for-index="newIndex" wx:key="newIndex"><view data-event-opts="{{[['tap',[['chooseOne',[index,newIndex,'$0'],[[['list','',index,'inputType']]]]]]]}}" class="box_item data-v-0f872228" style="{{(newItem.choose?'border:2rpx solid #2E80FE;color: #2E80FE;':'')}}" bindtap="__e">{{''+newItem.name+''}}<view class="ok data-v-0f872228" style="{{(newItem.choose?'':'display:none;')}}"><uni-icons vue-id="{{'0f51ef9a-1-'+index+'-'+newIndex}}" type="checkmarkempty" size="8" color="#fff" class="data-v-0f872228" bind:__l="__l"></uni-icons></view></view></block></view></view><view class="fg data-v-0f872228"></view></view></block><block wx:for="{{list2}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="chol data-v-0f872228"><view class="choose data-v-0f872228"><view class="title data-v-0f872228"><block wx:if="{{item.isRequired==1}}"><label class="_span data-v-0f872228">*</label></block>{{item.problemDesc}}</view><view class="desc data-v-0f872228">{{item.problemContent}}</view><view class="input-container data-v-0f872228" id="{{'input-container-'+index}}"><input class="form-input data-v-0f872228" type="text" placeholder="{{'请输入'+item.problemDesc}}" cursor-spacing="10" confirm-type="done" adjust-position="{{false}}" auto-height="{{false}}" data-event-opts="{{[['focus',[['handleInputFocus',[index]]]],['blur',[['handleInputBlur',['$event']]]],['input',[['__set_model',['$0','val','$event',[]],['form.data.'+(index+$root.g1)+'']],['handleInput',['$event']]]]]}}" value="{{form.data[index+$root.g2].val}}" bindfocus="__e" bindblur="__e" bindinput="__e"/></view></view><view class="fg data-v-0f872228"></view></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="chol data-v-0f872228"><view class="choose data-v-0f872228"><view class="title data-v-0f872228"><block wx:if="{{item.$orig.isRequired==1}}"><label class="_span data-v-0f872228">*</label></block>{{item.$orig.problemDesc}}</view><view class="desc up data-v-0f872228">{{item.$orig.problemContent}}</view><upload vue-id="{{'0f51ef9a-2-'+index}}" imagelist="{{form.data[item.g3].val}}" imgtype="{{item.g4}}" text="上传图片" imgsize="{{3}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^del',[['imgUpload']]]]}}" bind:upload="__e" bind:del="__e" class="data-v-0f872228" bind:__l="__l"></upload></view><view class="fg data-v-0f872228"></view></view></block><view style="height:300rpx;" class="data-v-0f872228"></view></view><view class="footer data-v-0f872228" style="{{(footerStyle)}}"><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="{{['righ','data-v-0f872228',(isSubmitting)?'submitting':'']}}" bindtap="__e">{{''+(isSubmitting?'提交中...':'立即下单')+''}}</view></view></view>