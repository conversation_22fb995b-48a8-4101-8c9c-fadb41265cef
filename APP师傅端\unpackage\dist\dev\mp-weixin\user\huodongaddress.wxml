<view class="page data-v-d480fd58"><block wx:if="{{$root.g0>0}}"><view class="data-v-d480fd58"><block wx:for="{{addressArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['choose',['$0'],[[['addressArr','',index]]]]]]]}}" class="address_item data-v-d480fd58" bindtap="__e"><view class="head data-v-d480fd58"><block wx:if="{{item.status==1}}"><view class="mr data-v-d480fd58">默认</view></block><text class="data-v-d480fd58">{{item.address}}</text><label class="_span data-v-d480fd58"></label></view><view class="body data-v-d480fd58"><view class="left data-v-d480fd58">{{item.addressInfo}}</view><view data-event-opts="{{[['tap',[['goUrl',['$0'],[[['addressArr','',index]]]]]]]}}" class="right data-v-d480fd58" catchtap="__e"><image src="../static/images/9369.png" mode class="data-v-d480fd58"></image></view></view><view class="foot data-v-d480fd58"><view class="box data-v-d480fd58">{{item.userName}}</view><view class="box data-v-d480fd58">{{item.sex==1?'（先生）':'（女士）'}}</view><view class="box data-v-d480fd58">{{item.mobile}}</view></view></view></block></view></block><block wx:if="{{$root.g1<total}}"><view data-event-opts="{{[['tap',[['loadMore',['$event']]]]]}}" class="load-more data-v-d480fd58" bindtap="__e">加载更多</view></block><view class="footer data-v-d480fd58"><view data-event-opts="{{[['tap',[['goUrlAdd',['../user/add_address']]]]]}}" class="btn data-v-d480fd58" bindtap="__e">新增地址</view></view></view>