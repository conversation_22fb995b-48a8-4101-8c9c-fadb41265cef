<view class="page data-v-027e28c9"><view class="header data-v-027e28c9"><image src="{{serviceInfo.cover}}" mode="scaleToFill" class="data-v-027e28c9"></image></view><view class="content data-v-027e28c9"><view class="card data-v-027e28c9"><view class="top data-v-027e28c9"><view class="title data-v-027e28c9">{{serviceInfo.title}}</view><block wx:if="{{serviceInfo.servicePriceType!=1}}"><view class="price data-v-027e28c9">{{"￥"+serviceInfo.price}}</view></block></view><view class="bottom data-v-027e28c9"><view class="left data-v-027e28c9">已选：</view><view class="right data-v-027e28c9"><block wx:for="{{chooseArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tag data-v-027e28c9">{{item.name}}</view></block></view></view></view><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="chol data-v-027e28c9"><view class="choose data-v-027e28c9"><view class="title data-v-027e28c9"><block wx:if="{{item.isRequired==1}}"><label class="_span data-v-027e28c9">*</label></block>{{item.problemDesc}}</view><view class="desc data-v-027e28c9">{{item.problemContent}}</view><view class="cho_box data-v-027e28c9"><block wx:for="{{item.options}}" wx:for-item="newItem" wx:for-index="newIndex" wx:key="newIndex"><view data-event-opts="{{[['tap',[['chooseOne',[index,newIndex,'$0'],[[['list','',index,'inputType']]]]]]]}}" class="box_item data-v-027e28c9" style="{{(newItem.choose?'border:2rpx solid #2E80FE;color: #2E80FE;':'')}}" bindtap="__e">{{''+newItem.name+''}}<view class="ok data-v-027e28c9" style="{{(newItem.choose?'':'display:none;')}}"><uni-icons vue-id="{{'4d47715d-1-'+index+'-'+newIndex}}" type="checkmarkempty" size="8" color="#fff" class="data-v-027e28c9" bind:__l="__l"></uni-icons></view></view></block></view></view><view class="fg data-v-027e28c9"></view></view></block><block wx:for="{{list2}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="chol data-v-027e28c9"><view class="choose data-v-027e28c9"><view class="title data-v-027e28c9"><block wx:if="{{item.isRequired==1}}"><label class="_span data-v-027e28c9">*</label></block>{{item.problemDesc}}</view><view class="desc data-v-027e28c9">{{item.problemContent}}</view><view class="input-container data-v-027e28c9" id="{{'input-container-'+index}}"><input class="form-input data-v-027e28c9" type="text" placeholder="{{'请输入'+item.problemDesc}}" cursor-spacing="10" confirm-type="done" adjust-position="{{false}}" auto-height="{{false}}" data-event-opts="{{[['focus',[['handleInputFocus',[index]]]],['blur',[['handleInputBlur',['$event']]]],['input',[['__set_model',['$0','val','$event',[]],['form.data.'+(index+$root.g1)+'']],['handleInput',['$event']]]]]}}" value="{{form.data[index+$root.g2].val}}" bindfocus="__e" bindblur="__e" bindinput="__e"/></view></view><view class="fg data-v-027e28c9"></view></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="chol data-v-027e28c9"><view class="choose data-v-027e28c9"><view class="title data-v-027e28c9"><block wx:if="{{item.$orig.isRequired==1}}"><label class="_span data-v-027e28c9">*</label></block>{{item.$orig.problemDesc}}</view><view class="desc up data-v-027e28c9">{{item.$orig.problemContent}}</view><upload vue-id="{{'4d47715d-2-'+index}}" imagelist="{{form.data[item.g3].val}}" imgtype="{{item.g4}}" text="上传图片" imgsize="{{3}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^del',[['imgUpload']]]]}}" bind:upload="__e" bind:del="__e" class="data-v-027e28c9" bind:__l="__l"></upload></view><view class="fg data-v-027e28c9"></view></view></block><view style="height:300rpx;" class="data-v-027e28c9"></view></view><view class="footer data-v-027e28c9" style="{{(footerStyle)}}"><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="{{['righ','data-v-027e28c9',(isSubmitting)?'submitting':'']}}" bindtap="__e">{{''+(isSubmitting?'提交中...':'立即下单')+''}}</view></view></view>