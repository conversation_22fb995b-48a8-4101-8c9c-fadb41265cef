@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-2b3ca7fc {
  min-height: 100vh;
  background: #f8f8f8;
  padding: 30rpx;
  /* Tab styles */
}
.page .tabs.data-v-2b3ca7fc {
  display: flex;
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 10rpx;
}
.page .tabs .tab_item.data-v-2b3ca7fc {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 16rpx;
  transition: all 0.3s;
}
.page .tabs .tab_item.active.data-v-2b3ca7fc {
  background: #2E80FE;
  color: #fff;
  font-weight: 500;
}
.page .order_item.data-v-2b3ca7fc {
  width: 690rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 22rpx 30rpx;
  position: relative;
  margin-bottom: 20rpx;
}
.page .order_item .top.data-v-2b3ca7fc {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .order_item .top .top_left.data-v-2b3ca7fc {
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .order_item .top .top_right.data-v-2b3ca7fc {
  font-size: 20rpx;
  font-weight: 400;
  color: #07C160;
}
.page .order_item .mid.data-v-2b3ca7fc {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .order_item .mid .mid_left.data-v-2b3ca7fc {
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
  max-width: 450rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .order_item .mid .mid_right.data-v-2b3ca7fc {
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
}
.page .order_item .bottom.data-v-2b3ca7fc {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .order_item .blue.data-v-2b3ca7fc {
  width: 10rpx;
  height: 24rpx;
  background: #2E80FE;
  position: absolute;
  top: 86rpx;
  left: 0;
}

