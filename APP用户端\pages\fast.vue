<template>
	<view class="page-container">
		<tabbar :cur="3"></tabbar>

		<view class="header">
			<view class="header-title">请选择服务类目</view>
		</view>

		<view class="content">
			<view class="service-list">
				<view class="service-card install-card" @tap="selectService(21, '安装服务')">
					<view class="card-content">
						<view class="service-text">上门安装</view>
						<view class="service-icon">
							<view class="icon-placeholder install-icon">🔧</view>
						</view>
					</view>
				</view>
				<view class="service-card repair-card" @tap="selectService(22, '维修服务')">
					<view class="card-content">
						<view class="service-text">上门维修</view>
						<view class="service-icon">
							<image
								src="https://zskj.asia/attachment/image/666/25/05/86fe36a3384346ef8e31bcc6b06f0c43.jpg"
								mode="aspectFit"
								class="icon-image"
							></image>
						</view>
					</view>
				</view>

				<view class="service-card clean-card" @tap="selectService(23, '清洗服务')">
					<view class="card-content">
						<view class="service-text">商用办公</view>
						<view class="service-icon">
							<image
								src="https://zskj.asia/attachment/image/666/25/05/9a10d8df55c64370893ae861d2ff9887.jpg"
								mode="aspectFit"
								class="icon-image"
							></image>
						</view>
					</view>
				</view>

				<view class="service-card rescue-card" @tap="selectService(46, '出行救援')">
					<view class="card-content">
						<view class="service-text">家政保洁</view>
						<view class="service-icon">
							<image
								src="https://zskj.asia/attachment/image/666/25/05/8df2da0eaeee41828c4535f6145a375c.jpg"
								mode="aspectFit"
								class="icon-image"
							></image>
						</view>
					</view>
				</view>
			</view>
		</view>

		<u-picker
			:show="showCategoryPicker"
			:columns="categoryColumns"
			@cancel="showCategoryPicker = false"
			@confirm="confirmCategory"
			keyName="name"
		></u-picker>

		<u-picker
			:show="showServicePicker"
			:columns="serviceColumns"
			@cancel="showServicePicker = false"
			@confirm="confirmService"
			keyName="title"
		></u-picker>
	</view>
</template>

<script>
	import tabbar from "@/components/tabbar.vue";

	export default {
		components: {
			tabbar
		},
		data() {
			return {
				serviceList: [
					{
						id: 21,
						name: '安装服务',
						displayName: '上门安装',
						icon: '🔧'
					},
					{
						id: 22,
						name: '维修服务',
						displayName: '上门维修',
						image: 'https://zskj.asia/attachment/image/666/25/05/86fe36a3384346ef8e31bcc6b06f0c43.jpg'
					},
					{
						id: 23,
						name: '清洗服务',
						displayName: '商用办公',
						image: 'https://zskj.asia/attachment/image/666/25/05/9a10d8df55c64370893ae861d2ff9887.jpg'
					},
					{
						id: 46,
						name: '出行救援',
						displayName: '家政保洁',
						image: 'https://zskj.asia/attachment/image/666/25/05/8df2da0eaeee41828c4535f6145a375c.jpg'
					}
				],
				showCategoryPicker: false,
				showServicePicker: false,
				categoryColumns: [[]],
				serviceColumns: [[]],
				selectedCategoryData: null,
				selectedServiceData: null,
				currentServiceId: null,
				currentServiceName: ''
			}
		},
		methods: {
			async selectService(serviceId, serviceName) {
				console.log('选择服务:', serviceId, serviceName);

				this.currentServiceId = serviceId;
				this.currentServiceName = serviceName;

				try {
					// 获取城市位置信息
					const city = uni.getStorageSync('city') || { position: '' };
					console.log('城市信息:', city);

					// 调用API获取服务分类数据
					const response = await this.$api.service.setserviceCate(city.position);
					console.log('API响应:', response);

					if (response && response.code === "200" && response.data) {
						// 查找对应的服务分类
						const selectedCategory = response.data.find(item => item.id === serviceId);
						console.log('找到的分类:', selectedCategory);

						if (selectedCategory && selectedCategory.children && selectedCategory.children.length > 0) {
							// 设置分类选择器数据
							this.categoryColumns = [selectedCategory.children];

							console.log('设置分类选择器数据:', this.categoryColumns);

							// 显示分类选择器
							this.showCategoryPicker = true;
						} else {
							console.log('没有子分类或子分类为空');
							uni.showToast({
								title: '该服务暂无子分类',
								icon: 'none'
							});
						}
					} else {
						console.log('API响应格式错误:', response);
						uni.showToast({
							title: '获取服务数据失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取服务数据失败:', error);
					uni.showToast({
						title: '网络错误，请重试: ' + error.message,
						icon: 'none'
					});
				}
			},

			// 确认分类选择
			confirmCategory(e) {
				console.log('选择的分类:', e);
				this.selectedCategoryData = e.value[0];

				if (this.selectedCategoryData && this.selectedCategoryData.serviceList && this.selectedCategoryData.serviceList.length > 0) {
					// 设置服务选择器数据
					this.serviceColumns = [this.selectedCategoryData.serviceList];
					
					// 隐藏分类选择器并显示服务选择器
					this.showCategoryPicker = false;
					this.showServicePicker = true;
				} else {
					uni.showToast({
						title: '该分类下暂无服务',
						icon: 'none'
					});
				}
			},

			// 确认服务选择
			confirmService(e) {
				console.log('选择的服务:', e);
				this.selectedServiceData = e.value[0];

				if (this.selectedServiceData) {
					// 隐藏服务选择器
					this.showServicePicker = false;
					// 跳转到详情页
					this.goToDetails(this.selectedServiceData.id);
				}
			},

			// 跳转到详情页
			goToDetails(id) {
				uni.navigateTo({
					url: `../user/commodity_details?id=${id}`,
					fail: (err) => {
						console.error("Navigation failed:", err);
						uni.showToast({
							title: "跳转失败: " + err.errMsg,
							icon: "none",
						});
					},
				});
			},

			// 关闭选择器
			closePickers() {
				this.showCategoryPicker = false;
				this.showServicePicker = false;
			}
		}
	}
</script>

<style scoped lang="scss">
	.page-container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.header {
		padding: 40rpx 30rpx 30rpx;
		background-color: #fff;

		.header-title {
			font-size: 36rpx;
			font-weight: 500;
			color: #333;
		}
	}



	.content {
		padding: 30rpx;
		padding-bottom: 150rpx; /* 为底部tabbar留出空间 */
	}

	.service-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.service-card {
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
		}

		.card-content {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.service-text {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
		}

		.service-icon {
			width: 80rpx;
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.icon-image {
				width: 80rpx;
				height: 80rpx;
				border-radius: 10rpx;
			}

			.icon-placeholder {
				font-size: 50rpx;
			}
		}
	}

	/* 不同服务卡片的背景色 */
	.repair-card {
		background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
	}

	.install-card {
		background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);

		.install-icon {
			color: #ff9800;
		}
	}

	.clean-card {
		background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
	}

	.rescue-card {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
	}
</style>