{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/master_my_order.vue?4b0d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/master_my_order.vue?a6f6", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/master_my_order.vue?bd74", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/master_my_order.vue?5949", "uni-app:///shifu/master_my_order.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/master_my_order.vue?c9b0", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/master_my_order.vue?46c0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "limit", "coachId", "shifuId", "tmplIds", "status", "list", "name", "value", "currentIndex", "page", "orderList", "pay_typeArr", "isLoading", "onReachBottom", "onPullDownRefresh", "methods", "dingyue", "console", "templateId", "templateCategoryId", "uni", "success", "selectedTmplIds", "fail", "loadMore", "refreshList", "setTimeout", "fetchOrders", "payType", "pageNum", "pageSize", "icon", "title", "filter", "map", "item", "goDetail", "url", "goUrl", "showConfirmModal", "content", "confirmText", "cancelText", "startFu", "id", "res", "queren", "updateHigh", "shiInfoid", "userId", "role", "getList", "handleHeader", "tab", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAA81B,CAAgB,82BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCwCl3B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC,UACA,IACA,IACA,IACA,8CACA;MACAC;MACAC,OACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACAC;MACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;MACAA;MACA;QAAA;UACAC;UACAC;QACA;MAAA;MACAC;QACAjB;QACAkB;UACAJ;UACA;UACA;UACAK;YACAL;YACA;cACA;cACA;gBACA;kBACA;gBACA;cACA;gBACA;cACA;cACAA;YACA;UACA;UACAA;QACA;QACAM;MACA;IACA;IACAC;MACA;MACA;MACA;MAEA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACAC;QACAN;MACA;IACA;IAEAO;MAAA;MAAA;MACA;QACA1B;QACA2B;QACAC;QACAC;MACA;QACA;UACAV;YACAW;YACAC;UACA;UACA;QACA;UACA;UACA,0BACAC;YAAA;UAAA,GACAC;YAAA,uCACAC;cACAP;YAAA;UAAA,CACA;UAEA;YACA;UACA;YACA;UACA;UAEA;UACA;QACA;QACA;QACA;MACA;QACA;QACA;QACAR;UACAY;UACAD;QACA;QACAd;QACA;MACA;IACA;IAEAmB;MACAhB;MACAA;QACAiB;MACA;IACA;IAEAC;MACAlB;QACAiB;MACA;IACA;IAEAE;MAAA;MACAnB;QACAY;QACAQ;QACAC;QACAC;QACArB;UACA;YACA;cACA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAsB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACAhB;gBACA;cAAA;gBAHAiB;gBAIA;kBACAzB;oBACAY;oBACAD;kBACA;kBACA;gBACA;kBACAX;oBACAY;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;kBACAY;kBACAD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAe;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAF;kBACAhB;gBACA;cAAA;gBAHAiB;gBAIA;kBACAzB;oBACAY;oBACAD;kBACA;kBACA;gBACA;kBACAX;oBACAY;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;kBACAY;kBACAD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAgB;MACA;MACA;QACA9B;QACA;MACA;MACA;MACA;QACA+B;MACA;QACA/B;QACA;MACA;MACA;QACAgC;QACAC;QACAtB;MACA;QACAX;MACA;QACAA;MACA;IACA;IAEAkC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QAAAC;MAAA;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;MACArC;MACA;IACA;IAEA;MACA;IACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChUA;AAAA;AAAA;AAAA;AAAqmD,CAAgB,yjDAAG,EAAC,C;;;;;;;;;;;ACAznD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/master_my_order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/master_my_order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./master_my_order.vue?vue&type=template&id=b530dfe4&scoped=true&\"\nvar renderjs\nimport script from \"./master_my_order.vue?vue&type=script&lang=js&\"\nexport * from \"./master_my_order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./master_my_order.vue?vue&type=style&index=0&id=b530dfe4&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b530dfe4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/master_my_order.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=template&id=b530dfe4&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loadmore/u-loadmore\" */ \"uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.orderList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = _vm.$util.timestampToTime(item.createTime * 1000)\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header_item\" v-for=\"(item, index) in list\" :key=\"index\" @click=\"handleHeader(item)\">\n\t\t\t\t<view :style=\"currentIndex == item.value ? 'color:#2E80FE;' : ''\">{{ item.name }}</view>\n\t\t\t\t<view class=\"blue\" :style=\"currentIndex == item.value ? '' : 'background-color:#fff;'\"></view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view @click=\"dingyue()\" class=\"main\">\n\t\t\t<view class=\"main_item\" v-for=\"(item, index) in orderList\" :key=\"index\" @click=\"goDetail(item)\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<view class=\"no\">单号：{{ item.orderCode }}</view>\n\t\t\t\t\t<view class=\"type\">{{ item.payType == -1 ? '已取消' : pay_typeArr[item.payType] }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"mid\">\n\t\t\t\t\t<view class=\"lef\">\n\t\t\t\t\t\t<image :src=\"item.goodsCover\" mode=\"\"></image>\n\t\t\t\t\t\t<text>{{ item.goodsName }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"righ\" v-if=\"item.payType == 7 || (item.payType == 7 && item.isAftermarket === 1)\">\n\t\t\t\t\t\t<view>￥{{ item.coachServicePrice }}</view>\n\t\t\t\t\t\t<view>x{{ item.num }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"bot\">\n\t\t\t\t\t<text>{{ $util.timestampToTime(item.createTime * 1000) }}</text>\n\t\t\t\t\t<view class=\"qzf\" v-if=\"item.payType === 3\" @click.stop=\"showConfirmModal(item, 'queren')\">\n\t\t\t\t\t\t确认到达\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"qzf\" v-if=\"item.payType === 5\" @click.stop=\"showConfirmModal(item, 'startFu')\">\n\t\t\t\t\t\t开始服务\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<u-loadmore :status=\"status\" @loadmore=\"loadMore\" />\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tlimit: 10,\n\t\t\tcoachId: '',\n\t\t\tshifuId: '',\n\t\t\ttmplIds: [\n\t\t\t\t'',\n\t\t\t\t'',\n\t\t\t\t'',\n\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t],\n\t\t\tstatus: 'loadmore',\n\t\t\tlist: [\n\t\t\t\t{ name: '全部', value: 0 },\n\t\t\t\t{ name: '待上门', value: 3 },\n\t\t\t\t{ name: '待服务', value: 5 },\n\t\t\t\t{ name: '服务中', value: 6 },\n\t\t\t\t{ name: '已完成', value: 7 },\n\t\t\t\t{ name: '售后', value: 8 },\n\t\t\t],\n\t\t\tcurrentIndex: 0,\n\t\t\tpage: 0,\n\t\t\torderList: [],\n\t\t\tpay_typeArr: ['', '待支付', '已支付', '已接单', '上门中', '待服务', '服务中', '已完成', '售后'],\n\t\t\tisLoading: false // Flag to prevent multiple API calls\n\t\t};\n\t},\n\tonReachBottom() {\n\t\tthis.loadMore();\n\t},\n\tonPullDownRefresh() {\n\t\tthis.refreshList();\n\t},\n\tmethods: {\n\tdingyue() {\n\t\tconsole.log('dingyue called');\n\t\tconst allTmplIds = this.tmplIds;\n\t\tconst requiredTmplId = '';\n\t\tif (allTmplIds.length < 3) {\n\t\t\tconsole.error(\"Not enough template IDs available:\", allTmplIds);\n\t\t\treturn;\n\t\t}\n\t\t// Ensure requiredTmplId is included, select 2 more randomly\n\t\tconst otherTmplIds = allTmplIds.filter(id => id !== requiredTmplId);\n\t\tconst shuffled = otherTmplIds.sort(() => 0.5 - Math.random());\n\t\tconst selectedTmplIds = [requiredTmplId, ...shuffled.slice(0, 2)];\n\t\tconsole.log(\"Selected template IDs:\", selectedTmplIds);\n\t\tconst templateData = selectedTmplIds.map((id, index) => ({\n\t\t\ttemplateId: id,\n\t\t\ttemplateCategoryId: index === 0 ? 10 : 5\n\t\t}));\n\t\tuni.requestSubscribeMessage({\n\t\t\ttmplIds: selectedTmplIds,\n\t\t\tsuccess: (res) => {\n\t\t\t\tconsole.log('requestSubscribeMessage result:', res);\n\t\t\t\tthis.templateCategoryIds = [];\n\t\t\t\tlet count = 0;\n\t\t\t\tselectedTmplIds.forEach((templId, index) => {\n\t\t\t\t\tconsole.log(`Template ${templId} status: ${res[templId]}`);\n\t\t\t\t\tif (res[templId] === 'accept') {\n\t\t\t\t\t\tconst templateCategoryId = templateData[index].templateCategoryId;\n\t\t\t\t\t\tif (templateCategoryId === 10) {\n\t\t\t\t\t\t\tfor (let i = 0; i < 15; i++) {\n\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.log('Accepted message push for template:', templId);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tconsole.log('Updated templateCategoryIds:', this.templateCategoryIds);\n\t\t\t},\n\t\t\tfail: (err) => {}\n\t\t});\n\t},\n\t\tloadMore() {\n\t\t\tif (this.status === 'nomore' || this.isLoading) return;\n\t\t\tthis.isLoading = true;\n\t\t\tthis.status = 'loading';\n\t\t\t\n\t\t\tconst nextPage = this.page + 1;\n\t\t\tthis.fetchOrders(nextPage, false);\n\t\t},\n\t\t\n\t\trefreshList() {\n\t\t\tthis.page = 0;\n\t\t\tthis.orderList = [];\n\t\t\tthis.status = 'loadmore';\n\t\t\tthis.getList();\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t}, 1000);\n\t\t},\n\t\t\n\t\tfetchOrders(pageNum, replaceList = true) {\n\t\t\treturn this.$api.shifu.master_Order({\n\t\t\t\tcoachId: this.shifuId,\n\t\t\t\tpayType: this.currentIndex === 8 ? 7 : this.currentIndex,\n\t\t\t\tpageNum: pageNum,\n\t\t\t\tpageSize: this.limit\n\t\t\t}).then(res => {\n\t\t\t\tif (res.code === '-1') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: res.msg || '没有更多数据'\n\t\t\t\t\t});\n\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t} else {\n\t\t\t\t\tconst list = Array.isArray(res.data.list) ? res.data.list : [];\n\t\t\t\t\tconst normalizedList = list\n\t\t\t\t\t\t.filter(item => this.currentIndex !== 8 || item.isAftermarket === 1)\n\t\t\t\t\t\t.map(item => ({\n\t\t\t\t\t\t\t...item,\n\t\t\t\t\t\t\tpayType: parseInt(item.payType)\n\t\t\t\t\t\t}));\n\t\t\t\t\t\n\t\t\t\t\tif (replaceList) {\n\t\t\t\t\t\tthis.orderList = normalizedList;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.orderList = [...this.orderList, ...normalizedList];\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.page = pageNum;\n\t\t\t\t\tthis.status = list.length < this.limit ? 'nomore' : 'loadmore';\n\t\t\t\t}\n\t\t\t\tthis.isLoading = false;\n\t\t\t\treturn res;\n\t\t\t}).catch(err => {\n\t\t\t\tthis.status = 'nomore';\n\t\t\t\tthis.isLoading = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tconsole.error('Error loading data:', err);\n\t\t\t\treturn Promise.reject(err);\n\t\t\t});\n\t\t},\n\t\t\n\t\tgoDetail(item) {\n\t\t\tuni.setStorageSync('orderdetails', item);\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/shifu/master_order_my?id=${item.id}`\n\t\t\t});\n\t\t},\n\t\t\n\t\tgoUrl(e) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: e\n\t\t\t});\n\t\t},\n\t\t\n\t\tshowConfirmModal(item, action) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: action === 'queren' ? '确认到达' : '开始服务',\n\t\t\t\tcontent: '请确认操作：' + (action === 'queren' ? '确认到达' : '开始服务'),\n\t\t\t\tconfirmText: '确定',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tif (action === 'queren') {\n\t\t\t\t\t\t\tthis.queren(item);\n\t\t\t\t\t\t} else if (action === 'startFu') {\n\t\t\t\t\t\t\tthis.startFu(item);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\tasync startFu(item) {\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.service.shifuqueren({\n\t\t\t\t\tid: item.id,\n\t\t\t\t\tpayType: 6\n\t\t\t\t});\n\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '操作成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.refreshList();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg || '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\tasync queren(item) {\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.service.shifuqueren({\n\t\t\t\t\tid: item.id,\n\t\t\t\t\tpayType: 5\n\t\t\t\t});\n\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '操作成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.refreshList();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg || '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\tupdateHigh(options) {\n\t\t\tconst shiInfo = uni.getStorageSync('shiInfo');\n\t\t\tif (!shiInfo) {\n\t\t\t\tconsole.log('No shiInfo, skipping updateHighlight');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tlet shiInfoid;\n\t\t\ttry {\n\t\t\t\tshiInfoid = JSON.parse(shiInfo);\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('Error parsing shiInfo:', e);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.$api.service.updataHighlight({\n\t\t\t\tuserId: shiInfoid.id,\n\t\t\t\trole: 2,\n\t\t\t\tpayType: options.tab\n\t\t\t}).then(res => {\n\t\t\t\tconsole.log(res);\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('Error updating highlight:', err);\n\t\t\t});\n\t\t},\n\t\t\n\t\tgetList() {\n\t\t\tif (this.isLoading) return;\n\t\t\tthis.isLoading = true;\n\t\t\tthis.status = 'loading';\n\t\t\tthis.fetchOrders(1, true);\n\t\t},\n\t\t\n\t\thandleHeader(item) {\n\t\t\tif (this.currentIndex === item.value) return;\n\t\t\tthis.currentIndex = item.value;\n\t\t\tthis.page = 0;\n\t\t\tthis.orderList = [];\n\t\t\tthis.status = 'loadmore';\n\t\t\tthis.getList();\n\t\t\tthis.updateHigh({ tab: item.value });\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tlet shiInfo = uni.getStorageSync('shiInfo') || '{}';\n\t\ttry {\n\t\t\tthis.shifuId = JSON.parse(shiInfo).id;\n\t\t} catch (e) {\n\t\t\tconsole.error('Error parsing shiInfo:', e);\n\t\t\tthis.shifuId = '';\n\t\t}\n\t\t\n\t\tif (options.tab) {\n\t\t\tthis.currentIndex = parseInt(options.tab);\n\t\t}\n\t\tthis.updateHigh(options);\n\t\tthis.getList();\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tbackground-color: #F8F8F8;\n\theight: 100vh;\n\toverflow: auto;\n\tpadding-top: 100rpx;\n\n\t.header {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: 100;\n\t\twidth: 750rpx;\n\t\theight: 100rpx;\n\t\tbackground: #FFFFFF;\n\t\tdisplay: flex;\n\t\tjustify-content: space-around;\n\t\talign-items: center;\n\n\t\t.header_item {\n\t\t\tmax-width: 85rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #999999;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\tflex-wrap: wrap;\n\n\t\t\t.blue {\n\t\t\t\tmargin-top: 8rpx;\n\t\t\t\twidth: 38rpx;\n\t\t\t\theight: 6rpx;\n\t\t\t\tbackground: #2E80FE;\n\t\t\t\tborder-radius: 4rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.main {\n\t\tpadding:20rpx 30rpx;\n\t\tmin-height: calc(100vh - 100rpx);\n\n\t\t.main_item {\n\t\t\twidth: 690rpx;\n\t\t\theight: 284rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder-radius: 24rpx;\n\t\t\tpadding: 28rpx 36rpx;\n\t\t\tmargin-bottom: 20rpx;\n\t\t\tbox-sizing: border-box;\n\n\t\t\t.head {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #999999;\n\n\t\t\t\t.no {\n\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.mid {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\n\t\t\t\t.lef {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\timage {\n\t\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\t\theight: 120rpx;\n\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\ttext {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\tmargin-left: 30rpx;\n\t\t\t\t\t\tmax-width: 350rpx;\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.righ {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t\ttext-align: right;\n\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.bot {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #999999;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\n\t\t\t\t.qzf {\n\t\t\t\t\twidth: 148rpx;\n\t\t\t\t\theight: 48rpx;\n\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\tborder-radius: 50rpx;\n\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tline-height: 48rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=style&index=0&id=b530dfe4&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=style&index=0&id=b530dfe4&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755654284671\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}